// Common types for the Trip.com clone application

export interface SearchFormData {
  searchType: 'flights' | 'hotels' | 'cars' | 'packages' | 'cruises' | 'things-to-do';
  origin?: string;
  destination?: string;
  checkIn?: Date;
  checkOut?: Date;
  passengers?: number;
  rooms?: number;
  guests?: number;
  class?: 'economy' | 'premium-economy' | 'business' | 'first';
}

export interface FlightSearchData {
  origin: string;
  destination: string;
  departureDate: Date;
  returnDate?: Date;
  passengers: number;
  class: 'economy' | 'premium-economy' | 'business' | 'first';
  tripType: 'round-trip' | 'one-way' | 'multi-city';
}

export interface HotelSearchData {
  destination: string;
  checkIn: Date;
  checkOut: Date;
  rooms: number;
  guests: number;
}

export interface CarRentalSearchData {
  pickupLocation: string;
  dropoffLocation?: string;
  pickupDate: Date;
  dropoffDate: Date;
  pickupTime: string;
  dropoffTime: string;
}

export interface Destination {
  id: string;
  name: string;
  country: string;
  image: string;
  description?: string;
  popular?: boolean;
}

export interface Deal {
  id: string;
  title: string;
  description: string;
  image: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  validUntil?: Date;
  type: 'flight' | 'hotel' | 'package' | 'car';
}

export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  isLoggedIn: boolean;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

// Component Props Types
export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
  className?: string;
}

export interface SelectProps {
  label?: string;
  options: Array<{ value: string; label: string }>;
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

export interface DatePickerProps {
  label?: string;
  value?: Date;
  onChange?: (date: Date) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
}
