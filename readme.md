# ✈️ Flymus

Flymus is a modern travel booking app. This is the flymus frontend application built with **Next.js**. It offers flight and hotel booking experiences. The goal of Flymus is to provide users with a seamless interface to search, explore, and book flights and hotels efficiently.

---

## 🚀 Features

- 🔍 Search and filter flights and hotels
- 📅 Booking management
- 💳 Payment integration (planned)
- 🌍 Multi-city and round-trip support
- 🖥️ Responsive design for all devices
- ⚡ Fast performance with Next.js features like SSR and API Routes

---
