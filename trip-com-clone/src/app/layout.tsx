import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Trip.com Official Site | Travel Deals and Promotions",
  description:
    "Book flights, hotels, cars, and vacation packages at Trip.com. Find the best travel deals and promotions for your next trip. Secure payment and 24/7 customer support.",
  keywords: "flights, hotels, cars, travel, vacation, deals, booking, Trip.com",
  authors: [{ name: "Trip.com" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "Trip.com Official Site | Travel Deals and Promotions",
    description:
      "Book flights, hotels, cars, and vacation packages at Trip.com. Find the best travel deals and promotions for your next trip.",
    type: "website",
    locale: "en_US",
    siteName: "Trip.com",
  },
  twitter: {
    card: "summary_large_image",
    title: "Trip.com Official Site | Travel Deals and Promotions",
    description:
      "Book flights, hotels, cars, and vacation packages at Trip.com. Find the best travel deals and promotions for your next trip.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
      </head>
      <body className="antialiased bg-white text-gray-900 min-h-screen">
        <div id="root" className="min-h-screen flex flex-col">
          {children}
        </div>
      </body>
    </html>
  );
}
