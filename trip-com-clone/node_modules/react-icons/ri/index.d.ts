// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const RiArrowDownBoxFill: IconType;
export declare const RiArrowDownBoxLine: IconType;
export declare const RiArrowDownCircleFill: IconType;
export declare const RiArrowDownCircleLine: IconType;
export declare const RiArrowDownDoubleFill: IconType;
export declare const RiArrowDownDoubleLine: IconType;
export declare const RiArrowDownFill: IconType;
export declare const RiArrowDownLine: IconType;
export declare const RiArrowDownSFill: IconType;
export declare const RiArrowDownSLine: IconType;
export declare const RiArrowDownWideFill: IconType;
export declare const RiArrowDownWideLine: IconType;
export declare const RiArrowDropDownFill: IconType;
export declare const RiArrowDropDownLine: IconType;
export declare const RiArrowDropLeftFill: IconType;
export declare const RiArrowDropLeftLine: IconType;
export declare const RiArrowDropRightFill: IconType;
export declare const RiArrowDropRightLine: IconType;
export declare const RiArrowDropUpFill: IconType;
export declare const RiArrowDropUpLine: IconType;
export declare const RiArrowGoBackFill: IconType;
export declare const RiArrowGoBackLine: IconType;
export declare const RiArrowGoForwardFill: IconType;
export declare const RiArrowGoForwardLine: IconType;
export declare const RiArrowLeftBoxFill: IconType;
export declare const RiArrowLeftBoxLine: IconType;
export declare const RiArrowLeftCircleFill: IconType;
export declare const RiArrowLeftCircleLine: IconType;
export declare const RiArrowLeftDoubleFill: IconType;
export declare const RiArrowLeftDoubleLine: IconType;
export declare const RiArrowLeftDownBoxFill: IconType;
export declare const RiArrowLeftDownBoxLine: IconType;
export declare const RiArrowLeftDownFill: IconType;
export declare const RiArrowLeftDownLine: IconType;
export declare const RiArrowLeftFill: IconType;
export declare const RiArrowLeftLine: IconType;
export declare const RiArrowLeftRightFill: IconType;
export declare const RiArrowLeftRightLine: IconType;
export declare const RiArrowLeftSFill: IconType;
export declare const RiArrowLeftSLine: IconType;
export declare const RiArrowLeftUpBoxFill: IconType;
export declare const RiArrowLeftUpBoxLine: IconType;
export declare const RiArrowLeftUpFill: IconType;
export declare const RiArrowLeftUpLine: IconType;
export declare const RiArrowLeftWideFill: IconType;
export declare const RiArrowLeftWideLine: IconType;
export declare const RiArrowRightBoxFill: IconType;
export declare const RiArrowRightBoxLine: IconType;
export declare const RiArrowRightCircleFill: IconType;
export declare const RiArrowRightCircleLine: IconType;
export declare const RiArrowRightDoubleFill: IconType;
export declare const RiArrowRightDoubleLine: IconType;
export declare const RiArrowRightDownBoxFill: IconType;
export declare const RiArrowRightDownBoxLine: IconType;
export declare const RiArrowRightDownFill: IconType;
export declare const RiArrowRightDownLine: IconType;
export declare const RiArrowRightFill: IconType;
export declare const RiArrowRightLine: IconType;
export declare const RiArrowRightSFill: IconType;
export declare const RiArrowRightSLine: IconType;
export declare const RiArrowRightUpBoxFill: IconType;
export declare const RiArrowRightUpBoxLine: IconType;
export declare const RiArrowRightUpFill: IconType;
export declare const RiArrowRightUpLine: IconType;
export declare const RiArrowRightWideFill: IconType;
export declare const RiArrowRightWideLine: IconType;
export declare const RiArrowTurnBackFill: IconType;
export declare const RiArrowTurnBackLine: IconType;
export declare const RiArrowTurnForwardFill: IconType;
export declare const RiArrowTurnForwardLine: IconType;
export declare const RiArrowUpBoxFill: IconType;
export declare const RiArrowUpBoxLine: IconType;
export declare const RiArrowUpCircleFill: IconType;
export declare const RiArrowUpCircleLine: IconType;
export declare const RiArrowUpDoubleFill: IconType;
export declare const RiArrowUpDoubleLine: IconType;
export declare const RiArrowUpDownFill: IconType;
export declare const RiArrowUpDownLine: IconType;
export declare const RiArrowUpFill: IconType;
export declare const RiArrowUpLine: IconType;
export declare const RiArrowUpSFill: IconType;
export declare const RiArrowUpSLine: IconType;
export declare const RiArrowUpWideFill: IconType;
export declare const RiArrowUpWideLine: IconType;
export declare const RiCollapseDiagonal2Fill: IconType;
export declare const RiCollapseDiagonal2Line: IconType;
export declare const RiCollapseDiagonalFill: IconType;
export declare const RiCollapseDiagonalLine: IconType;
export declare const RiCollapseHorizontalFill: IconType;
export declare const RiCollapseHorizontalLine: IconType;
export declare const RiCollapseVerticalFill: IconType;
export declare const RiCollapseVerticalLine: IconType;
export declare const RiContractLeftFill: IconType;
export declare const RiContractLeftLine: IconType;
export declare const RiContractLeftRightFill: IconType;
export declare const RiContractLeftRightLine: IconType;
export declare const RiContractRightFill: IconType;
export declare const RiContractRightLine: IconType;
export declare const RiContractUpDownFill: IconType;
export declare const RiContractUpDownLine: IconType;
export declare const RiCornerDownLeftFill: IconType;
export declare const RiCornerDownLeftLine: IconType;
export declare const RiCornerDownRightFill: IconType;
export declare const RiCornerDownRightLine: IconType;
export declare const RiCornerLeftDownFill: IconType;
export declare const RiCornerLeftDownLine: IconType;
export declare const RiCornerLeftUpFill: IconType;
export declare const RiCornerLeftUpLine: IconType;
export declare const RiCornerRightDownFill: IconType;
export declare const RiCornerRightDownLine: IconType;
export declare const RiCornerRightUpFill: IconType;
export declare const RiCornerRightUpLine: IconType;
export declare const RiCornerUpLeftDoubleFill: IconType;
export declare const RiCornerUpLeftDoubleLine: IconType;
export declare const RiCornerUpLeftFill: IconType;
export declare const RiCornerUpLeftLine: IconType;
export declare const RiCornerUpRightDoubleFill: IconType;
export declare const RiCornerUpRightDoubleLine: IconType;
export declare const RiCornerUpRightFill: IconType;
export declare const RiCornerUpRightLine: IconType;
export declare const RiDragMove2Fill: IconType;
export declare const RiDragMove2Line: IconType;
export declare const RiDragMoveFill: IconType;
export declare const RiDragMoveLine: IconType;
export declare const RiExpandDiagonal2Fill: IconType;
export declare const RiExpandDiagonal2Line: IconType;
export declare const RiExpandDiagonalFill: IconType;
export declare const RiExpandDiagonalLine: IconType;
export declare const RiExpandDiagonalS2Fill: IconType;
export declare const RiExpandDiagonalS2Line: IconType;
export declare const RiExpandDiagonalSFill: IconType;
export declare const RiExpandDiagonalSLine: IconType;
export declare const RiExpandHeightFill: IconType;
export declare const RiExpandHeightLine: IconType;
export declare const RiExpandHorizontalFill: IconType;
export declare const RiExpandHorizontalLine: IconType;
export declare const RiExpandHorizontalSFill: IconType;
export declare const RiExpandHorizontalSLine: IconType;
export declare const RiExpandLeftFill: IconType;
export declare const RiExpandLeftLine: IconType;
export declare const RiExpandLeftRightFill: IconType;
export declare const RiExpandLeftRightLine: IconType;
export declare const RiExpandRightFill: IconType;
export declare const RiExpandRightLine: IconType;
export declare const RiExpandUpDownFill: IconType;
export declare const RiExpandUpDownLine: IconType;
export declare const RiExpandVerticalFill: IconType;
export declare const RiExpandVerticalLine: IconType;
export declare const RiExpandVerticalSFill: IconType;
export declare const RiExpandVerticalSLine: IconType;
export declare const RiExpandWidthFill: IconType;
export declare const RiExpandWidthLine: IconType;
export declare const RiScrollToBottomFill: IconType;
export declare const RiScrollToBottomLine: IconType;
export declare const RiSkipDownFill: IconType;
export declare const RiSkipDownLine: IconType;
export declare const RiSkipLeftFill: IconType;
export declare const RiSkipLeftLine: IconType;
export declare const RiSkipRightFill: IconType;
export declare const RiSkipRightLine: IconType;
export declare const RiSkipUpFill: IconType;
export declare const RiSkipUpLine: IconType;
export declare const RiAncientGateFill: IconType;
export declare const RiAncientGateLine: IconType;
export declare const RiAncientPavilionFill: IconType;
export declare const RiAncientPavilionLine: IconType;
export declare const RiBankFill: IconType;
export declare const RiBankLine: IconType;
export declare const RiBuilding2Fill: IconType;
export declare const RiBuilding2Line: IconType;
export declare const RiBuilding3Fill: IconType;
export declare const RiBuilding3Line: IconType;
export declare const RiBuilding4Fill: IconType;
export declare const RiBuilding4Line: IconType;
export declare const RiBuildingFill: IconType;
export declare const RiBuildingLine: IconType;
export declare const RiCommunityFill: IconType;
export declare const RiCommunityLine: IconType;
export declare const RiGovernmentFill: IconType;
export declare const RiGovernmentLine: IconType;
export declare const RiHome2Fill: IconType;
export declare const RiHome2Line: IconType;
export declare const RiHome3Fill: IconType;
export declare const RiHome3Line: IconType;
export declare const RiHome4Fill: IconType;
export declare const RiHome4Line: IconType;
export declare const RiHome5Fill: IconType;
export declare const RiHome5Line: IconType;
export declare const RiHome6Fill: IconType;
export declare const RiHome6Line: IconType;
export declare const RiHome7Fill: IconType;
export declare const RiHome7Line: IconType;
export declare const RiHome8Fill: IconType;
export declare const RiHome8Line: IconType;
export declare const RiHome9Fill: IconType;
export declare const RiHome9Line: IconType;
export declare const RiHomeFill: IconType;
export declare const RiHomeGearFill: IconType;
export declare const RiHomeGearLine: IconType;
export declare const RiHomeHeartFill: IconType;
export declare const RiHomeHeartLine: IconType;
export declare const RiHomeLine: IconType;
export declare const RiHomeOfficeFill: IconType;
export declare const RiHomeOfficeLine: IconType;
export declare const RiHomeSmile2Fill: IconType;
export declare const RiHomeSmile2Line: IconType;
export declare const RiHomeSmileFill: IconType;
export declare const RiHomeSmileLine: IconType;
export declare const RiHomeWifiFill: IconType;
export declare const RiHomeWifiLine: IconType;
export declare const RiHospitalFill: IconType;
export declare const RiHospitalLine: IconType;
export declare const RiHotelFill: IconType;
export declare const RiHotelLine: IconType;
export declare const RiSchoolFill: IconType;
export declare const RiSchoolLine: IconType;
export declare const RiStore2Fill: IconType;
export declare const RiStore2Line: IconType;
export declare const RiStore3Fill: IconType;
export declare const RiStore3Line: IconType;
export declare const RiStoreFill: IconType;
export declare const RiStoreLine: IconType;
export declare const RiTentFill: IconType;
export declare const RiTentLine: IconType;
export declare const RiAdvertisementFill: IconType;
export declare const RiAdvertisementLine: IconType;
export declare const RiArchive2Fill: IconType;
export declare const RiArchive2Line: IconType;
export declare const RiArchiveDrawerFill: IconType;
export declare const RiArchiveDrawerLine: IconType;
export declare const RiArchiveFill: IconType;
export declare const RiArchiveLine: IconType;
export declare const RiArchiveStackFill: IconType;
export declare const RiArchiveStackLine: IconType;
export declare const RiAtFill: IconType;
export declare const RiAtLine: IconType;
export declare const RiAttachmentFill: IconType;
export declare const RiAttachmentLine: IconType;
export declare const RiAwardFill: IconType;
export declare const RiAwardLine: IconType;
export declare const RiBarChart2Fill: IconType;
export declare const RiBarChart2Line: IconType;
export declare const RiBarChartBoxAiFill: IconType;
export declare const RiBarChartBoxAiLine: IconType;
export declare const RiBarChartBoxFill: IconType;
export declare const RiBarChartBoxLine: IconType;
export declare const RiBarChartFill: IconType;
export declare const RiBarChartGroupedFill: IconType;
export declare const RiBarChartGroupedLine: IconType;
export declare const RiBarChartHorizontalFill: IconType;
export declare const RiBarChartHorizontalLine: IconType;
export declare const RiBarChartLine: IconType;
export declare const RiBookmark2Fill: IconType;
export declare const RiBookmark2Line: IconType;
export declare const RiBookmark3Fill: IconType;
export declare const RiBookmark3Line: IconType;
export declare const RiBookmarkFill: IconType;
export declare const RiBookmarkLine: IconType;
export declare const RiBriefcase2Fill: IconType;
export declare const RiBriefcase2Line: IconType;
export declare const RiBriefcase3Fill: IconType;
export declare const RiBriefcase3Line: IconType;
export declare const RiBriefcase4Fill: IconType;
export declare const RiBriefcase4Line: IconType;
export declare const RiBriefcase5Fill: IconType;
export declare const RiBriefcase5Line: IconType;
export declare const RiBriefcaseFill: IconType;
export declare const RiBriefcaseLine: IconType;
export declare const RiBubbleChartFill: IconType;
export declare const RiBubbleChartLine: IconType;
export declare const RiCalculatorFill: IconType;
export declare const RiCalculatorLine: IconType;
export declare const RiCalendar2Fill: IconType;
export declare const RiCalendar2Line: IconType;
export declare const RiCalendarCheckFill: IconType;
export declare const RiCalendarCheckLine: IconType;
export declare const RiCalendarCloseFill: IconType;
export declare const RiCalendarCloseLine: IconType;
export declare const RiCalendarEventFill: IconType;
export declare const RiCalendarEventLine: IconType;
export declare const RiCalendarFill: IconType;
export declare const RiCalendarLine: IconType;
export declare const RiCalendarScheduleFill: IconType;
export declare const RiCalendarScheduleLine: IconType;
export declare const RiCalendarTodoFill: IconType;
export declare const RiCalendarTodoLine: IconType;
export declare const RiCloudFill: IconType;
export declare const RiCloudLine: IconType;
export declare const RiCloudOffFill: IconType;
export declare const RiCloudOffLine: IconType;
export declare const RiCopyleftFill: IconType;
export declare const RiCopyleftLine: IconType;
export declare const RiCopyrightFill: IconType;
export declare const RiCopyrightLine: IconType;
export declare const RiCreativeCommonsByFill: IconType;
export declare const RiCreativeCommonsByLine: IconType;
export declare const RiCreativeCommonsFill: IconType;
export declare const RiCreativeCommonsLine: IconType;
export declare const RiCreativeCommonsNcFill: IconType;
export declare const RiCreativeCommonsNcLine: IconType;
export declare const RiCreativeCommonsNdFill: IconType;
export declare const RiCreativeCommonsNdLine: IconType;
export declare const RiCreativeCommonsSaFill: IconType;
export declare const RiCreativeCommonsSaLine: IconType;
export declare const RiCreativeCommonsZeroFill: IconType;
export declare const RiCreativeCommonsZeroLine: IconType;
export declare const RiCustomerService2Fill: IconType;
export declare const RiCustomerService2Line: IconType;
export declare const RiCustomerServiceFill: IconType;
export declare const RiCustomerServiceLine: IconType;
export declare const RiDonutChartFill: IconType;
export declare const RiDonutChartLine: IconType;
export declare const RiFlag2Fill: IconType;
export declare const RiFlag2Line: IconType;
export declare const RiFlagFill: IconType;
export declare const RiFlagLine: IconType;
export declare const RiFlagOffFill: IconType;
export declare const RiFlagOffLine: IconType;
export declare const RiGlobalFill: IconType;
export declare const RiGlobalLine: IconType;
export declare const RiHonourFill: IconType;
export declare const RiHonourLine: IconType;
export declare const RiIdCardFill: IconType;
export declare const RiIdCardLine: IconType;
export declare const RiInbox2Fill: IconType;
export declare const RiInbox2Line: IconType;
export declare const RiInboxArchiveFill: IconType;
export declare const RiInboxArchiveLine: IconType;
export declare const RiInboxFill: IconType;
export declare const RiInboxLine: IconType;
export declare const RiInboxUnarchiveFill: IconType;
export declare const RiInboxUnarchiveLine: IconType;
export declare const RiInfoCardFill: IconType;
export declare const RiInfoCardLine: IconType;
export declare const RiLineChartFill: IconType;
export declare const RiLineChartLine: IconType;
export declare const RiLinksFill: IconType;
export declare const RiLinksLine: IconType;
export declare const RiMailAddFill: IconType;
export declare const RiMailAddLine: IconType;
export declare const RiMailAiFill: IconType;
export declare const RiMailAiLine: IconType;
export declare const RiMailCheckFill: IconType;
export declare const RiMailCheckLine: IconType;
export declare const RiMailCloseFill: IconType;
export declare const RiMailCloseLine: IconType;
export declare const RiMailDownloadFill: IconType;
export declare const RiMailDownloadLine: IconType;
export declare const RiMailFill: IconType;
export declare const RiMailForbidFill: IconType;
export declare const RiMailForbidLine: IconType;
export declare const RiMailLine: IconType;
export declare const RiMailLockFill: IconType;
export declare const RiMailLockLine: IconType;
export declare const RiMailOpenFill: IconType;
export declare const RiMailOpenLine: IconType;
export declare const RiMailSendFill: IconType;
export declare const RiMailSendLine: IconType;
export declare const RiMailSettingsFill: IconType;
export declare const RiMailSettingsLine: IconType;
export declare const RiMailStarFill: IconType;
export declare const RiMailStarLine: IconType;
export declare const RiMailUnreadFill: IconType;
export declare const RiMailUnreadLine: IconType;
export declare const RiMailVolumeFill: IconType;
export declare const RiMailVolumeLine: IconType;
export declare const RiMedal2Fill: IconType;
export declare const RiMedal2Line: IconType;
export declare const RiMedalFill: IconType;
export declare const RiMedalLine: IconType;
export declare const RiMegaphoneFill: IconType;
export declare const RiMegaphoneLine: IconType;
export declare const RiPassExpiredFill: IconType;
export declare const RiPassExpiredLine: IconType;
export declare const RiPassPendingFill: IconType;
export declare const RiPassPendingLine: IconType;
export declare const RiPassValidFill: IconType;
export declare const RiPassValidLine: IconType;
export declare const RiPieChart2Fill: IconType;
export declare const RiPieChart2Line: IconType;
export declare const RiPieChartBoxFill: IconType;
export declare const RiPieChartBoxLine: IconType;
export declare const RiPieChartFill: IconType;
export declare const RiPieChartLine: IconType;
export declare const RiPresentationFill: IconType;
export declare const RiPresentationLine: IconType;
export declare const RiPrinterCloudFill: IconType;
export declare const RiPrinterCloudLine: IconType;
export declare const RiPrinterFill: IconType;
export declare const RiPrinterLine: IconType;
export declare const RiProfileFill: IconType;
export declare const RiProfileLine: IconType;
export declare const RiProjector2Fill: IconType;
export declare const RiProjector2Line: IconType;
export declare const RiProjectorFill: IconType;
export declare const RiProjectorLine: IconType;
export declare const RiRecordMailFill: IconType;
export declare const RiRecordMailLine: IconType;
export declare const RiRegisteredFill: IconType;
export declare const RiRegisteredLine: IconType;
export declare const RiReplyAllFill: IconType;
export declare const RiReplyAllLine: IconType;
export declare const RiReplyFill: IconType;
export declare const RiReplyLine: IconType;
export declare const RiSendPlane2Fill: IconType;
export declare const RiSendPlane2Line: IconType;
export declare const RiSendPlaneFill: IconType;
export declare const RiSendPlaneLine: IconType;
export declare const RiSeoFill: IconType;
export declare const RiSeoLine: IconType;
export declare const RiServiceFill: IconType;
export declare const RiServiceLine: IconType;
export declare const RiShakeHandsFill: IconType;
export declare const RiShakeHandsLine: IconType;
export declare const RiSlideshow2Fill: IconType;
export declare const RiSlideshow2Line: IconType;
export declare const RiSlideshow3Fill: IconType;
export declare const RiSlideshow3Line: IconType;
export declare const RiSlideshow4Fill: IconType;
export declare const RiSlideshow4Line: IconType;
export declare const RiSlideshowFill: IconType;
export declare const RiSlideshowLine: IconType;
export declare const RiStackFill: IconType;
export declare const RiStackLine: IconType;
export declare const RiTrademarkFill: IconType;
export declare const RiTrademarkLine: IconType;
export declare const RiTriangularFlagFill: IconType;
export declare const RiTriangularFlagLine: IconType;
export declare const RiVerifiedBadgeFill: IconType;
export declare const RiVerifiedBadgeLine: IconType;
export declare const RiWindow2Fill: IconType;
export declare const RiWindow2Line: IconType;
export declare const RiWindowFill: IconType;
export declare const RiWindowLine: IconType;
export declare const RiChat1Fill: IconType;
export declare const RiChat1Line: IconType;
export declare const RiChat2Fill: IconType;
export declare const RiChat2Line: IconType;
export declare const RiChat3Fill: IconType;
export declare const RiChat3Line: IconType;
export declare const RiChat4Fill: IconType;
export declare const RiChat4Line: IconType;
export declare const RiChatAiFill: IconType;
export declare const RiChatAiLine: IconType;
export declare const RiChatCheckFill: IconType;
export declare const RiChatCheckLine: IconType;
export declare const RiChatDeleteFill: IconType;
export declare const RiChatDeleteLine: IconType;
export declare const RiChatDownloadFill: IconType;
export declare const RiChatDownloadLine: IconType;
export declare const RiChatFollowUpFill: IconType;
export declare const RiChatFollowUpLine: IconType;
export declare const RiChatForwardFill: IconType;
export declare const RiChatForwardLine: IconType;
export declare const RiChatHeartFill: IconType;
export declare const RiChatHeartLine: IconType;
export declare const RiChatHistoryFill: IconType;
export declare const RiChatHistoryLine: IconType;
export declare const RiChatNewFill: IconType;
export declare const RiChatNewLine: IconType;
export declare const RiChatOffFill: IconType;
export declare const RiChatOffLine: IconType;
export declare const RiChatPollFill: IconType;
export declare const RiChatPollLine: IconType;
export declare const RiChatPrivateFill: IconType;
export declare const RiChatPrivateLine: IconType;
export declare const RiChatQuoteFill: IconType;
export declare const RiChatQuoteLine: IconType;
export declare const RiChatSearchFill: IconType;
export declare const RiChatSearchLine: IconType;
export declare const RiChatSettingsFill: IconType;
export declare const RiChatSettingsLine: IconType;
export declare const RiChatSmile2Fill: IconType;
export declare const RiChatSmile2Line: IconType;
export declare const RiChatSmile3Fill: IconType;
export declare const RiChatSmile3Line: IconType;
export declare const RiChatSmileAiFill: IconType;
export declare const RiChatSmileAiLine: IconType;
export declare const RiChatSmileFill: IconType;
export declare const RiChatSmileLine: IconType;
export declare const RiChatThreadFill: IconType;
export declare const RiChatThreadLine: IconType;
export declare const RiChatUnreadFill: IconType;
export declare const RiChatUnreadLine: IconType;
export declare const RiChatUploadFill: IconType;
export declare const RiChatUploadLine: IconType;
export declare const RiChatVoiceAiFill: IconType;
export declare const RiChatVoiceAiLine: IconType;
export declare const RiChatVoiceFill: IconType;
export declare const RiChatVoiceLine: IconType;
export declare const RiDiscussFill: IconType;
export declare const RiDiscussLine: IconType;
export declare const RiEmojiStickerFill: IconType;
export declare const RiEmojiStickerLine: IconType;
export declare const RiFeedbackFill: IconType;
export declare const RiFeedbackLine: IconType;
export declare const RiMessage2Fill: IconType;
export declare const RiMessage2Line: IconType;
export declare const RiMessage3Fill: IconType;
export declare const RiMessage3Line: IconType;
export declare const RiMessageFill: IconType;
export declare const RiMessageLine: IconType;
export declare const RiQuestionAnswerFill: IconType;
export declare const RiQuestionAnswerLine: IconType;
export declare const RiQuestionnaireFill: IconType;
export declare const RiQuestionnaireLine: IconType;
export declare const RiSpeakAiFill: IconType;
export declare const RiSpeakAiLine: IconType;
export declare const RiSpeakFill: IconType;
export declare const RiSpeakLine: IconType;
export declare const RiVideoChatFill: IconType;
export declare const RiVideoChatLine: IconType;
export declare const RiAlignItemBottomFill: IconType;
export declare const RiAlignItemBottomLine: IconType;
export declare const RiAlignItemHorizontalCenterFill: IconType;
export declare const RiAlignItemHorizontalCenterLine: IconType;
export declare const RiAlignItemLeftFill: IconType;
export declare const RiAlignItemLeftLine: IconType;
export declare const RiAlignItemRightFill: IconType;
export declare const RiAlignItemRightLine: IconType;
export declare const RiAlignItemTopFill: IconType;
export declare const RiAlignItemTopLine: IconType;
export declare const RiAlignItemVerticalCenterFill: IconType;
export declare const RiAlignItemVerticalCenterLine: IconType;
export declare const RiAnticlockwise2Fill: IconType;
export declare const RiAnticlockwise2Line: IconType;
export declare const RiAnticlockwiseFill: IconType;
export declare const RiAnticlockwiseLine: IconType;
export declare const RiArtboard2Fill: IconType;
export declare const RiArtboard2Line: IconType;
export declare const RiArtboardFill: IconType;
export declare const RiArtboardLine: IconType;
export declare const RiBallPenFill: IconType;
export declare const RiBallPenLine: IconType;
export declare const RiBlurOffFill: IconType;
export declare const RiBlurOffLine: IconType;
export declare const RiBrush2Fill: IconType;
export declare const RiBrush2Line: IconType;
export declare const RiBrush3Fill: IconType;
export declare const RiBrush3Line: IconType;
export declare const RiBrush4Fill: IconType;
export declare const RiBrush4Line: IconType;
export declare const RiBrushAiFill: IconType;
export declare const RiBrushAiLine: IconType;
export declare const RiBrushFill: IconType;
export declare const RiBrushLine: IconType;
export declare const RiCircleFill: IconType;
export declare const RiCircleLine: IconType;
export declare const RiClockwise2Fill: IconType;
export declare const RiClockwise2Line: IconType;
export declare const RiClockwiseFill: IconType;
export declare const RiClockwiseLine: IconType;
export declare const RiCollageFill: IconType;
export declare const RiCollageLine: IconType;
export declare const RiColorFilterAiFill: IconType;
export declare const RiColorFilterAiLine: IconType;
export declare const RiColorFilterFill: IconType;
export declare const RiColorFilterLine: IconType;
export declare const RiCompasses2Fill: IconType;
export declare const RiCompasses2Line: IconType;
export declare const RiCompassesFill: IconType;
export declare const RiCompassesLine: IconType;
export declare const RiContrast2Fill: IconType;
export declare const RiContrast2Line: IconType;
export declare const RiContrastDrop2Fill: IconType;
export declare const RiContrastDrop2Line: IconType;
export declare const RiContrastDropFill: IconType;
export declare const RiContrastDropLine: IconType;
export declare const RiContrastFill: IconType;
export declare const RiContrastLine: IconType;
export declare const RiCrop2Fill: IconType;
export declare const RiCrop2Line: IconType;
export declare const RiCropFill: IconType;
export declare const RiCropLine: IconType;
export declare const RiCrosshair2Fill: IconType;
export declare const RiCrosshair2Line: IconType;
export declare const RiCrosshairFill: IconType;
export declare const RiCrosshairLine: IconType;
export declare const RiDragDropFill: IconType;
export declare const RiDragDropLine: IconType;
export declare const RiDropFill: IconType;
export declare const RiDropLine: IconType;
export declare const RiEdit2Fill: IconType;
export declare const RiEdit2Line: IconType;
export declare const RiEditBoxFill: IconType;
export declare const RiEditBoxLine: IconType;
export declare const RiEditCircleFill: IconType;
export declare const RiEditCircleLine: IconType;
export declare const RiEditFill: IconType;
export declare const RiEditLine: IconType;
export declare const RiEraserFill: IconType;
export declare const RiEraserLine: IconType;
export declare const RiFlipHorizontal2Fill: IconType;
export declare const RiFlipHorizontal2Line: IconType;
export declare const RiFlipHorizontalFill: IconType;
export declare const RiFlipHorizontalLine: IconType;
export declare const RiFlipVertical2Fill: IconType;
export declare const RiFlipVertical2Line: IconType;
export declare const RiFlipVerticalFill: IconType;
export declare const RiFlipVerticalLine: IconType;
export declare const RiFocus2Fill: IconType;
export declare const RiFocus2Line: IconType;
export declare const RiFocus3Fill: IconType;
export declare const RiFocus3Line: IconType;
export declare const RiFocusFill: IconType;
export declare const RiFocusLine: IconType;
export declare const RiGridFill: IconType;
export declare const RiGridLine: IconType;
export declare const RiHammerFill: IconType;
export declare const RiHammerLine: IconType;
export declare const RiHexagonFill: IconType;
export declare const RiHexagonLine: IconType;
export declare const RiInkBottleFill: IconType;
export declare const RiInkBottleLine: IconType;
export declare const RiInputMethodFill: IconType;
export declare const RiInputMethodLine: IconType;
export declare const RiLayout2Fill: IconType;
export declare const RiLayout2Line: IconType;
export declare const RiLayout3Fill: IconType;
export declare const RiLayout3Line: IconType;
export declare const RiLayout4Fill: IconType;
export declare const RiLayout4Line: IconType;
export declare const RiLayout5Fill: IconType;
export declare const RiLayout5Line: IconType;
export declare const RiLayout6Fill: IconType;
export declare const RiLayout6Line: IconType;
export declare const RiLayoutBottom2Fill: IconType;
export declare const RiLayoutBottom2Line: IconType;
export declare const RiLayoutBottomFill: IconType;
export declare const RiLayoutBottomLine: IconType;
export declare const RiLayoutColumnFill: IconType;
export declare const RiLayoutColumnLine: IconType;
export declare const RiLayoutFill: IconType;
export declare const RiLayoutGrid2Fill: IconType;
export declare const RiLayoutGrid2Line: IconType;
export declare const RiLayoutGridFill: IconType;
export declare const RiLayoutGridLine: IconType;
export declare const RiLayoutHorizontalFill: IconType;
export declare const RiLayoutHorizontalLine: IconType;
export declare const RiLayoutLeft2Fill: IconType;
export declare const RiLayoutLeft2Line: IconType;
export declare const RiLayoutLeftFill: IconType;
export declare const RiLayoutLeftLine: IconType;
export declare const RiLayoutLine: IconType;
export declare const RiLayoutMasonryFill: IconType;
export declare const RiLayoutMasonryLine: IconType;
export declare const RiLayoutRight2Fill: IconType;
export declare const RiLayoutRight2Line: IconType;
export declare const RiLayoutRightFill: IconType;
export declare const RiLayoutRightLine: IconType;
export declare const RiLayoutRowFill: IconType;
export declare const RiLayoutRowLine: IconType;
export declare const RiLayoutTop2Fill: IconType;
export declare const RiLayoutTop2Line: IconType;
export declare const RiLayoutTopFill: IconType;
export declare const RiLayoutTopLine: IconType;
export declare const RiLayoutVerticalFill: IconType;
export declare const RiLayoutVerticalLine: IconType;
export declare const RiMagicFill: IconType;
export declare const RiMagicLine: IconType;
export declare const RiMarkPenFill: IconType;
export declare const RiMarkPenLine: IconType;
export declare const RiMarkupFill: IconType;
export declare const RiMarkupLine: IconType;
export declare const RiOctagonFill: IconType;
export declare const RiOctagonLine: IconType;
export declare const RiPaintBrushFill: IconType;
export declare const RiPaintBrushLine: IconType;
export declare const RiPaintFill: IconType;
export declare const RiPaintLine: IconType;
export declare const RiPaletteFill: IconType;
export declare const RiPaletteLine: IconType;
export declare const RiPantoneFill: IconType;
export declare const RiPantoneLine: IconType;
export declare const RiPenNibFill: IconType;
export declare const RiPenNibLine: IconType;
export declare const RiPencilFill: IconType;
export declare const RiPencilLine: IconType;
export declare const RiPencilRuler2Fill: IconType;
export declare const RiPencilRuler2Line: IconType;
export declare const RiPencilRulerFill: IconType;
export declare const RiPencilRulerLine: IconType;
export declare const RiPentagonFill: IconType;
export declare const RiPentagonLine: IconType;
export declare const RiQuillPenAiFill: IconType;
export declare const RiQuillPenAiLine: IconType;
export declare const RiQuillPenFill: IconType;
export declare const RiQuillPenLine: IconType;
export declare const RiRectangleFill: IconType;
export declare const RiRectangleLine: IconType;
export declare const RiRuler2Fill: IconType;
export declare const RiRuler2Line: IconType;
export declare const RiRulerFill: IconType;
export declare const RiRulerLine: IconType;
export declare const RiScissors2Fill: IconType;
export declare const RiScissors2Line: IconType;
export declare const RiScissorsCutFill: IconType;
export declare const RiScissorsCutLine: IconType;
export declare const RiScissorsFill: IconType;
export declare const RiScissorsLine: IconType;
export declare const RiScreenshot2Fill: IconType;
export declare const RiScreenshot2Line: IconType;
export declare const RiScreenshotFill: IconType;
export declare const RiScreenshotLine: IconType;
export declare const RiShadowFill: IconType;
export declare const RiShadowLine: IconType;
export declare const RiShape2Fill: IconType;
export declare const RiShape2Line: IconType;
export declare const RiShapeFill: IconType;
export declare const RiShapeLine: IconType;
export declare const RiShapesFill: IconType;
export declare const RiShapesLine: IconType;
export declare const RiSipFill: IconType;
export declare const RiSipLine: IconType;
export declare const RiSliceFill: IconType;
export declare const RiSliceLine: IconType;
export declare const RiSquareFill: IconType;
export declare const RiSquareLine: IconType;
export declare const RiTBoxFill: IconType;
export declare const RiTBoxLine: IconType;
export declare const RiTableAltFill: IconType;
export declare const RiTableAltLine: IconType;
export declare const RiTableFill: IconType;
export declare const RiTableLine: IconType;
export declare const RiToolsFill: IconType;
export declare const RiToolsLine: IconType;
export declare const RiTriangleFill: IconType;
export declare const RiTriangleLine: IconType;
export declare const RiBracesFill: IconType;
export declare const RiBracesLine: IconType;
export declare const RiBracketsFill: IconType;
export declare const RiBracketsLine: IconType;
export declare const RiBug2Fill: IconType;
export declare const RiBug2Line: IconType;
export declare const RiBugFill: IconType;
export declare const RiBugLine: IconType;
export declare const RiCodeAiFill: IconType;
export declare const RiCodeAiLine: IconType;
export declare const RiCodeBoxFill: IconType;
export declare const RiCodeBoxLine: IconType;
export declare const RiCodeFill: IconType;
export declare const RiCodeLine: IconType;
export declare const RiCodeSFill: IconType;
export declare const RiCodeSLine: IconType;
export declare const RiCodeSSlashFill: IconType;
export declare const RiCodeSSlashLine: IconType;
export declare const RiCommandFill: IconType;
export declare const RiCommandLine: IconType;
export declare const RiCss3Fill: IconType;
export declare const RiCss3Line: IconType;
export declare const RiCursorFill: IconType;
export declare const RiCursorLine: IconType;
export declare const RiGitBranchFill: IconType;
export declare const RiGitBranchLine: IconType;
export declare const RiGitClosePullRequestFill: IconType;
export declare const RiGitClosePullRequestLine: IconType;
export declare const RiGitCommitFill: IconType;
export declare const RiGitCommitLine: IconType;
export declare const RiGitForkFill: IconType;
export declare const RiGitForkLine: IconType;
export declare const RiGitMergeFill: IconType;
export declare const RiGitMergeLine: IconType;
export declare const RiGitPrDraftFill: IconType;
export declare const RiGitPrDraftLine: IconType;
export declare const RiGitPullRequestFill: IconType;
export declare const RiGitPullRequestLine: IconType;
export declare const RiGitRepositoryCommitsFill: IconType;
export declare const RiGitRepositoryCommitsLine: IconType;
export declare const RiGitRepositoryFill: IconType;
export declare const RiGitRepositoryLine: IconType;
export declare const RiGitRepositoryPrivateFill: IconType;
export declare const RiGitRepositoryPrivateLine: IconType;
export declare const RiHtml5Fill: IconType;
export declare const RiHtml5Line: IconType;
export declare const RiJavascriptFill: IconType;
export declare const RiJavascriptLine: IconType;
export declare const RiParenthesesFill: IconType;
export declare const RiParenthesesLine: IconType;
export declare const RiPhpFill: IconType;
export declare const RiPhpLine: IconType;
export declare const RiPuzzle2Fill: IconType;
export declare const RiPuzzle2Line: IconType;
export declare const RiPuzzleFill: IconType;
export declare const RiPuzzleLine: IconType;
export declare const RiTerminalBoxFill: IconType;
export declare const RiTerminalBoxLine: IconType;
export declare const RiTerminalFill: IconType;
export declare const RiTerminalLine: IconType;
export declare const RiTerminalWindowFill: IconType;
export declare const RiTerminalWindowLine: IconType;
export declare const RiAirplayFill: IconType;
export declare const RiAirplayLine: IconType;
export declare const RiBarcodeBoxFill: IconType;
export declare const RiBarcodeBoxLine: IconType;
export declare const RiBarcodeFill: IconType;
export declare const RiBarcodeLine: IconType;
export declare const RiBaseStationFill: IconType;
export declare const RiBaseStationLine: IconType;
export declare const RiBattery2ChargeFill: IconType;
export declare const RiBattery2ChargeLine: IconType;
export declare const RiBattery2Fill: IconType;
export declare const RiBattery2Line: IconType;
export declare const RiBatteryChargeFill: IconType;
export declare const RiBatteryChargeLine: IconType;
export declare const RiBatteryFill: IconType;
export declare const RiBatteryLine: IconType;
export declare const RiBatteryLowFill: IconType;
export declare const RiBatteryLowLine: IconType;
export declare const RiBatterySaverFill: IconType;
export declare const RiBatterySaverLine: IconType;
export declare const RiBatteryShareFill: IconType;
export declare const RiBatteryShareLine: IconType;
export declare const RiBluetoothConnectFill: IconType;
export declare const RiBluetoothConnectLine: IconType;
export declare const RiBluetoothFill: IconType;
export declare const RiBluetoothLine: IconType;
export declare const RiCastFill: IconType;
export declare const RiCastLine: IconType;
export declare const RiCellphoneFill: IconType;
export declare const RiCellphoneLine: IconType;
export declare const RiComputerFill: IconType;
export declare const RiComputerLine: IconType;
export declare const RiCpuFill: IconType;
export declare const RiCpuLine: IconType;
export declare const RiDashboard2Fill: IconType;
export declare const RiDashboard2Line: IconType;
export declare const RiDashboard3Fill: IconType;
export declare const RiDashboard3Line: IconType;
export declare const RiDatabase2Fill: IconType;
export declare const RiDatabase2Line: IconType;
export declare const RiDatabaseFill: IconType;
export declare const RiDatabaseLine: IconType;
export declare const RiDeviceFill: IconType;
export declare const RiDeviceLine: IconType;
export declare const RiDeviceRecoverFill: IconType;
export declare const RiDeviceRecoverLine: IconType;
export declare const RiDualSim1Fill: IconType;
export declare const RiDualSim1Line: IconType;
export declare const RiDualSim2Fill: IconType;
export declare const RiDualSim2Line: IconType;
export declare const RiFingerprint2Fill: IconType;
export declare const RiFingerprint2Line: IconType;
export declare const RiFingerprintFill: IconType;
export declare const RiFingerprintLine: IconType;
export declare const RiGamepadFill: IconType;
export declare const RiGamepadLine: IconType;
export declare const RiGpsFill: IconType;
export declare const RiGpsLine: IconType;
export declare const RiGradienterFill: IconType;
export declare const RiGradienterLine: IconType;
export declare const RiHardDrive2Fill: IconType;
export declare const RiHardDrive2Line: IconType;
export declare const RiHardDrive3Fill: IconType;
export declare const RiHardDrive3Line: IconType;
export declare const RiHardDriveFill: IconType;
export declare const RiHardDriveLine: IconType;
export declare const RiHotspotFill: IconType;
export declare const RiHotspotLine: IconType;
export declare const RiInstallFill: IconType;
export declare const RiInstallLine: IconType;
export declare const RiInstanceFill: IconType;
export declare const RiInstanceLine: IconType;
export declare const RiKeyboardBoxFill: IconType;
export declare const RiKeyboardBoxLine: IconType;
export declare const RiKeyboardFill: IconType;
export declare const RiKeyboardLine: IconType;
export declare const RiMacFill: IconType;
export declare const RiMacLine: IconType;
export declare const RiMacbookFill: IconType;
export declare const RiMacbookLine: IconType;
export declare const RiMobileDownloadFill: IconType;
export declare const RiMobileDownloadLine: IconType;
export declare const RiMouseFill: IconType;
export declare const RiMouseLine: IconType;
export declare const RiPhoneFill: IconType;
export declare const RiPhoneFindFill: IconType;
export declare const RiPhoneFindLine: IconType;
export declare const RiPhoneLine: IconType;
export declare const RiPhoneLockFill: IconType;
export declare const RiPhoneLockLine: IconType;
export declare const RiQrCodeFill: IconType;
export declare const RiQrCodeLine: IconType;
export declare const RiQrScan2Fill: IconType;
export declare const RiQrScan2Line: IconType;
export declare const RiQrScanFill: IconType;
export declare const RiQrScanLine: IconType;
export declare const RiRadarFill: IconType;
export declare const RiRadarLine: IconType;
export declare const RiRam2Fill: IconType;
export declare const RiRam2Line: IconType;
export declare const RiRamFill: IconType;
export declare const RiRamLine: IconType;
export declare const RiRemoteControl2Fill: IconType;
export declare const RiRemoteControl2Line: IconType;
export declare const RiRemoteControlFill: IconType;
export declare const RiRemoteControlLine: IconType;
export declare const RiRestartFill: IconType;
export declare const RiRestartLine: IconType;
export declare const RiRfidFill: IconType;
export declare const RiRfidLine: IconType;
export declare const RiRotateLockFill: IconType;
export declare const RiRotateLockLine: IconType;
export declare const RiRouterFill: IconType;
export declare const RiRouterLine: IconType;
export declare const RiRssFill: IconType;
export declare const RiRssLine: IconType;
export declare const RiSave2Fill: IconType;
export declare const RiSave2Line: IconType;
export declare const RiSave3Fill: IconType;
export declare const RiSave3Line: IconType;
export declare const RiSaveFill: IconType;
export declare const RiSaveLine: IconType;
export declare const RiScan2Fill: IconType;
export declare const RiScan2Line: IconType;
export declare const RiScanFill: IconType;
export declare const RiScanLine: IconType;
export declare const RiSdCardFill: IconType;
export declare const RiSdCardLine: IconType;
export declare const RiSdCardMiniFill: IconType;
export declare const RiSdCardMiniLine: IconType;
export declare const RiSensorFill: IconType;
export declare const RiSensorLine: IconType;
export declare const RiServerFill: IconType;
export declare const RiServerLine: IconType;
export declare const RiShutDownFill: IconType;
export declare const RiShutDownLine: IconType;
export declare const RiSignalWifi1Fill: IconType;
export declare const RiSignalWifi1Line: IconType;
export declare const RiSignalWifi2Fill: IconType;
export declare const RiSignalWifi2Line: IconType;
export declare const RiSignalWifi3Fill: IconType;
export declare const RiSignalWifi3Line: IconType;
export declare const RiSignalWifiErrorFill: IconType;
export declare const RiSignalWifiErrorLine: IconType;
export declare const RiSignalWifiFill: IconType;
export declare const RiSignalWifiLine: IconType;
export declare const RiSignalWifiOffFill: IconType;
export declare const RiSignalWifiOffLine: IconType;
export declare const RiSimCard2Fill: IconType;
export declare const RiSimCard2Line: IconType;
export declare const RiSimCardFill: IconType;
export declare const RiSimCardLine: IconType;
export declare const RiSmartphoneFill: IconType;
export declare const RiSmartphoneLine: IconType;
export declare const RiTabletFill: IconType;
export declare const RiTabletLine: IconType;
export declare const RiTv2Fill: IconType;
export declare const RiTv2Line: IconType;
export declare const RiTvFill: IconType;
export declare const RiTvLine: IconType;
export declare const RiUDiskFill: IconType;
export declare const RiUDiskLine: IconType;
export declare const RiUninstallFill: IconType;
export declare const RiUninstallLine: IconType;
export declare const RiUsbFill: IconType;
export declare const RiUsbLine: IconType;
export declare const RiWifiFill: IconType;
export declare const RiWifiLine: IconType;
export declare const RiWifiOffFill: IconType;
export declare const RiWifiOffLine: IconType;
export declare const RiWirelessChargingFill: IconType;
export declare const RiWirelessChargingLine: IconType;
export declare const RiArticleFill: IconType;
export declare const RiArticleLine: IconType;
export declare const RiBillFill: IconType;
export declare const RiBillLine: IconType;
export declare const RiBook2Fill: IconType;
export declare const RiBook2Line: IconType;
export declare const RiBook3Fill: IconType;
export declare const RiBook3Line: IconType;
export declare const RiBookFill: IconType;
export declare const RiBookLine: IconType;
export declare const RiBookMarkedFill: IconType;
export declare const RiBookMarkedLine: IconType;
export declare const RiBookOpenFill: IconType;
export declare const RiBookOpenLine: IconType;
export declare const RiBookReadFill: IconType;
export declare const RiBookReadLine: IconType;
export declare const RiBookletFill: IconType;
export declare const RiBookletLine: IconType;
export declare const RiClipboardFill: IconType;
export declare const RiClipboardLine: IconType;
export declare const RiContactsBook2Fill: IconType;
export declare const RiContactsBook2Line: IconType;
export declare const RiContactsBook3Fill: IconType;
export declare const RiContactsBook3Line: IconType;
export declare const RiContactsBookFill: IconType;
export declare const RiContactsBookLine: IconType;
export declare const RiContactsBookUploadFill: IconType;
export declare const RiContactsBookUploadLine: IconType;
export declare const RiContractFill: IconType;
export declare const RiContractLine: IconType;
export declare const RiDraftFill: IconType;
export declare const RiDraftLine: IconType;
export declare const RiFile2Fill: IconType;
export declare const RiFile2Line: IconType;
export declare const RiFile3Fill: IconType;
export declare const RiFile3Line: IconType;
export declare const RiFile4Fill: IconType;
export declare const RiFile4Line: IconType;
export declare const RiFileAddFill: IconType;
export declare const RiFileAddLine: IconType;
export declare const RiFileChart2Fill: IconType;
export declare const RiFileChart2Line: IconType;
export declare const RiFileChartFill: IconType;
export declare const RiFileChartLine: IconType;
export declare const RiFileCheckFill: IconType;
export declare const RiFileCheckLine: IconType;
export declare const RiFileCloseFill: IconType;
export declare const RiFileCloseLine: IconType;
export declare const RiFileCloudFill: IconType;
export declare const RiFileCloudLine: IconType;
export declare const RiFileCodeFill: IconType;
export declare const RiFileCodeLine: IconType;
export declare const RiFileCopy2Fill: IconType;
export declare const RiFileCopy2Line: IconType;
export declare const RiFileCopyFill: IconType;
export declare const RiFileCopyLine: IconType;
export declare const RiFileDamageFill: IconType;
export declare const RiFileDamageLine: IconType;
export declare const RiFileDownloadFill: IconType;
export declare const RiFileDownloadLine: IconType;
export declare const RiFileEditFill: IconType;
export declare const RiFileEditLine: IconType;
export declare const RiFileExcel2Fill: IconType;
export declare const RiFileExcel2Line: IconType;
export declare const RiFileExcelFill: IconType;
export declare const RiFileExcelLine: IconType;
export declare const RiFileFill: IconType;
export declare const RiFileForbidFill: IconType;
export declare const RiFileForbidLine: IconType;
export declare const RiFileGifFill: IconType;
export declare const RiFileGifLine: IconType;
export declare const RiFileHistoryFill: IconType;
export declare const RiFileHistoryLine: IconType;
export declare const RiFileHwpFill: IconType;
export declare const RiFileHwpLine: IconType;
export declare const RiFileImageFill: IconType;
export declare const RiFileImageLine: IconType;
export declare const RiFileInfoFill: IconType;
export declare const RiFileInfoLine: IconType;
export declare const RiFileLine: IconType;
export declare const RiFileList2Fill: IconType;
export declare const RiFileList2Line: IconType;
export declare const RiFileList3Fill: IconType;
export declare const RiFileList3Line: IconType;
export declare const RiFileListFill: IconType;
export declare const RiFileListLine: IconType;
export declare const RiFileLockFill: IconType;
export declare const RiFileLockLine: IconType;
export declare const RiFileMarkedFill: IconType;
export declare const RiFileMarkedLine: IconType;
export declare const RiFileMusicFill: IconType;
export declare const RiFileMusicLine: IconType;
export declare const RiFilePaper2Fill: IconType;
export declare const RiFilePaper2Line: IconType;
export declare const RiFilePaperFill: IconType;
export declare const RiFilePaperLine: IconType;
export declare const RiFilePdf2Fill: IconType;
export declare const RiFilePdf2Line: IconType;
export declare const RiFilePdfFill: IconType;
export declare const RiFilePdfLine: IconType;
export declare const RiFilePpt2Fill: IconType;
export declare const RiFilePpt2Line: IconType;
export declare const RiFilePptFill: IconType;
export declare const RiFilePptLine: IconType;
export declare const RiFileReduceFill: IconType;
export declare const RiFileReduceLine: IconType;
export declare const RiFileSearchFill: IconType;
export declare const RiFileSearchLine: IconType;
export declare const RiFileSettingsFill: IconType;
export declare const RiFileSettingsLine: IconType;
export declare const RiFileShield2Fill: IconType;
export declare const RiFileShield2Line: IconType;
export declare const RiFileShieldFill: IconType;
export declare const RiFileShieldLine: IconType;
export declare const RiFileShredFill: IconType;
export declare const RiFileShredLine: IconType;
export declare const RiFileTextFill: IconType;
export declare const RiFileTextLine: IconType;
export declare const RiFileTransferFill: IconType;
export declare const RiFileTransferLine: IconType;
export declare const RiFileUnknowFill: IconType;
export declare const RiFileUnknowLine: IconType;
export declare const RiFileUploadFill: IconType;
export declare const RiFileUploadLine: IconType;
export declare const RiFileUserFill: IconType;
export declare const RiFileUserLine: IconType;
export declare const RiFileVideoFill: IconType;
export declare const RiFileVideoLine: IconType;
export declare const RiFileWarningFill: IconType;
export declare const RiFileWarningLine: IconType;
export declare const RiFileWord2Fill: IconType;
export declare const RiFileWord2Line: IconType;
export declare const RiFileWordFill: IconType;
export declare const RiFileWordLine: IconType;
export declare const RiFileZipFill: IconType;
export declare const RiFileZipLine: IconType;
export declare const RiFolder2Fill: IconType;
export declare const RiFolder2Line: IconType;
export declare const RiFolder3Fill: IconType;
export declare const RiFolder3Line: IconType;
export declare const RiFolder4Fill: IconType;
export declare const RiFolder4Line: IconType;
export declare const RiFolder5Fill: IconType;
export declare const RiFolder5Line: IconType;
export declare const RiFolder6Fill: IconType;
export declare const RiFolder6Line: IconType;
export declare const RiFolderAddFill: IconType;
export declare const RiFolderAddLine: IconType;
export declare const RiFolderChart2Fill: IconType;
export declare const RiFolderChart2Line: IconType;
export declare const RiFolderChartFill: IconType;
export declare const RiFolderChartLine: IconType;
export declare const RiFolderCheckFill: IconType;
export declare const RiFolderCheckLine: IconType;
export declare const RiFolderCloseFill: IconType;
export declare const RiFolderCloseLine: IconType;
export declare const RiFolderCloudFill: IconType;
export declare const RiFolderCloudLine: IconType;
export declare const RiFolderDownloadFill: IconType;
export declare const RiFolderDownloadLine: IconType;
export declare const RiFolderFill: IconType;
export declare const RiFolderForbidFill: IconType;
export declare const RiFolderForbidLine: IconType;
export declare const RiFolderHistoryFill: IconType;
export declare const RiFolderHistoryLine: IconType;
export declare const RiFolderImageFill: IconType;
export declare const RiFolderImageLine: IconType;
export declare const RiFolderInfoFill: IconType;
export declare const RiFolderInfoLine: IconType;
export declare const RiFolderKeyholeFill: IconType;
export declare const RiFolderKeyholeLine: IconType;
export declare const RiFolderLine: IconType;
export declare const RiFolderLockFill: IconType;
export declare const RiFolderLockLine: IconType;
export declare const RiFolderMusicFill: IconType;
export declare const RiFolderMusicLine: IconType;
export declare const RiFolderOpenFill: IconType;
export declare const RiFolderOpenLine: IconType;
export declare const RiFolderReceivedFill: IconType;
export declare const RiFolderReceivedLine: IconType;
export declare const RiFolderReduceFill: IconType;
export declare const RiFolderReduceLine: IconType;
export declare const RiFolderSettingsFill: IconType;
export declare const RiFolderSettingsLine: IconType;
export declare const RiFolderSharedFill: IconType;
export declare const RiFolderSharedLine: IconType;
export declare const RiFolderShield2Fill: IconType;
export declare const RiFolderShield2Line: IconType;
export declare const RiFolderShieldFill: IconType;
export declare const RiFolderShieldLine: IconType;
export declare const RiFolderTransferFill: IconType;
export declare const RiFolderTransferLine: IconType;
export declare const RiFolderUnknowFill: IconType;
export declare const RiFolderUnknowLine: IconType;
export declare const RiFolderUploadFill: IconType;
export declare const RiFolderUploadLine: IconType;
export declare const RiFolderUserFill: IconType;
export declare const RiFolderUserLine: IconType;
export declare const RiFolderVideoFill: IconType;
export declare const RiFolderVideoLine: IconType;
export declare const RiFolderWarningFill: IconType;
export declare const RiFolderWarningLine: IconType;
export declare const RiFolderZipFill: IconType;
export declare const RiFolderZipLine: IconType;
export declare const RiFoldersFill: IconType;
export declare const RiFoldersLine: IconType;
export declare const RiKeynoteFill: IconType;
export declare const RiKeynoteLine: IconType;
export declare const RiMarkdownFill: IconType;
export declare const RiMarkdownLine: IconType;
export declare const RiNewsFill: IconType;
export declare const RiNewsLine: IconType;
export declare const RiNewspaperFill: IconType;
export declare const RiNewspaperLine: IconType;
export declare const RiNumbersFill: IconType;
export declare const RiNumbersLine: IconType;
export declare const RiPagesFill: IconType;
export declare const RiPagesLine: IconType;
export declare const RiReceiptFill: IconType;
export declare const RiReceiptLine: IconType;
export declare const RiStickyNote2Fill: IconType;
export declare const RiStickyNote2Line: IconType;
export declare const RiStickyNoteAddFill: IconType;
export declare const RiStickyNoteAddLine: IconType;
export declare const RiStickyNoteFill: IconType;
export declare const RiStickyNoteLine: IconType;
export declare const RiSurveyFill: IconType;
export declare const RiSurveyLine: IconType;
export declare const RiTaskFill: IconType;
export declare const RiTaskLine: IconType;
export declare const RiTodoFill: IconType;
export declare const RiTodoLine: IconType;
export declare const RiAB: IconType;
export declare const RiAiGenerate2: IconType;
export declare const RiAiGenerateText: IconType;
export declare const RiAiGenerate: IconType;
export declare const RiAlignBottom: IconType;
export declare const RiAlignCenter: IconType;
export declare const RiAlignJustify: IconType;
export declare const RiAlignLeft: IconType;
export declare const RiAlignRight: IconType;
export declare const RiAlignTop: IconType;
export declare const RiAlignVertically: IconType;
export declare const RiAsterisk: IconType;
export declare const RiAttachment2: IconType;
export declare const RiBold: IconType;
export declare const RiBringForward: IconType;
export declare const RiBringToFront: IconType;
export declare const RiCalendarView: IconType;
export declare const RiCarouselView: IconType;
export declare const RiCodeBlock: IconType;
export declare const RiCodeView: IconType;
export declare const RiCustomSize: IconType;
export declare const RiDeleteColumn: IconType;
export declare const RiDeleteRow: IconType;
export declare const RiDoubleQuotesL: IconType;
export declare const RiDoubleQuotesR: IconType;
export declare const RiDraggable: IconType;
export declare const RiDropdownList: IconType;
export declare const RiEmphasisCn: IconType;
export declare const RiEmphasis: IconType;
export declare const RiEnglishInput: IconType;
export declare const RiFlowChart: IconType;
export declare const RiFocusMode: IconType;
export declare const RiFontColor: IconType;
export declare const RiFontFamily: IconType;
export declare const RiFontMono: IconType;
export declare const RiFontSansSerif: IconType;
export declare const RiFontSans: IconType;
export declare const RiFontSize2: IconType;
export declare const RiFontSizeAi: IconType;
export declare const RiFontSize: IconType;
export declare const RiFormatClear: IconType;
export declare const RiFormula: IconType;
export declare const RiFunctions: IconType;
export declare const RiGalleryView2: IconType;
export declare const RiGalleryView: IconType;
export declare const RiH1: IconType;
export declare const RiH2: IconType;
export declare const RiH3: IconType;
export declare const RiH4: IconType;
export declare const RiH5: IconType;
export declare const RiH6: IconType;
export declare const RiHand: IconType;
export declare const RiHashtag: IconType;
export declare const RiHeading: IconType;
export declare const RiIndentDecrease: IconType;
export declare const RiIndentIncrease: IconType;
export declare const RiInfoI: IconType;
export declare const RiInputCursorMove: IconType;
export declare const RiInputField: IconType;
export declare const RiInsertColumnLeft: IconType;
export declare const RiInsertColumnRight: IconType;
export declare const RiInsertRowBottom: IconType;
export declare const RiInsertRowTop: IconType;
export declare const RiItalic: IconType;
export declare const RiKanbanView2: IconType;
export declare const RiKanbanView: IconType;
export declare const RiLetterSpacing2: IconType;
export declare const RiLineHeight2: IconType;
export declare const RiLineHeight: IconType;
export declare const RiLinkM: IconType;
export declare const RiLinkUnlinkM: IconType;
export declare const RiLinkUnlink: IconType;
export declare const RiLink: IconType;
export declare const RiListCheck2: IconType;
export declare const RiListCheck3: IconType;
export declare const RiListCheck: IconType;
export declare const RiListIndefinite: IconType;
export declare const RiListOrdered2: IconType;
export declare const RiListOrdered: IconType;
export declare const RiListRadio: IconType;
export declare const RiListUnordered: IconType;
export declare const RiListView: IconType;
export declare const RiMergeCellsHorizontal: IconType;
export declare const RiMergeCellsVertical: IconType;
export declare const RiMindMap: IconType;
export declare const RiNodeTree: IconType;
export declare const RiNumber0: IconType;
export declare const RiNumber1: IconType;
export declare const RiNumber2: IconType;
export declare const RiNumber3: IconType;
export declare const RiNumber4: IconType;
export declare const RiNumber5: IconType;
export declare const RiNumber6: IconType;
export declare const RiNumber7: IconType;
export declare const RiNumber8: IconType;
export declare const RiNumber9: IconType;
export declare const RiOmega: IconType;
export declare const RiOrganizationChart: IconType;
export declare const RiOverline: IconType;
export declare const RiPageSeparator: IconType;
export declare const RiParagraph: IconType;
export declare const RiPinyinInput: IconType;
export declare const RiQuestionMark: IconType;
export declare const RiQuoteText: IconType;
export declare const RiRoundedCorner: IconType;
export declare const RiSendBackward: IconType;
export declare const RiSendToBack: IconType;
export declare const RiSeparator: IconType;
export declare const RiSingleQuotesL: IconType;
export declare const RiSingleQuotesR: IconType;
export declare const RiSketching: IconType;
export declare const RiSlashCommands2: IconType;
export declare const RiSlashCommands: IconType;
export declare const RiSlideshowView: IconType;
export declare const RiSortAlphabetAsc: IconType;
export declare const RiSortAlphabetDesc: IconType;
export declare const RiSortAsc: IconType;
export declare const RiSortDesc: IconType;
export declare const RiSortNumberAsc: IconType;
export declare const RiSortNumberDesc: IconType;
export declare const RiSpace: IconType;
export declare const RiSplitCellsHorizontal: IconType;
export declare const RiSplitCellsVertical: IconType;
export declare const RiSquareRoot: IconType;
export declare const RiStackedView: IconType;
export declare const RiStrikethrough2: IconType;
export declare const RiStrikethrough: IconType;
export declare const RiSubscript2: IconType;
export declare const RiSubscript: IconType;
export declare const RiSuperscript2: IconType;
export declare const RiSuperscript: IconType;
export declare const RiTable2: IconType;
export declare const RiTable3: IconType;
export declare const RiTableView: IconType;
export declare const RiTextBlock: IconType;
export declare const RiTextDirectionL: IconType;
export declare const RiTextDirectionR: IconType;
export declare const RiTextSnippet: IconType;
export declare const RiTextSpacing: IconType;
export declare const RiTextWrap: IconType;
export declare const RiText: IconType;
export declare const RiTimelineView: IconType;
export declare const RiTranslate2: IconType;
export declare const RiTranslateAi2: IconType;
export declare const RiTranslateAi: IconType;
export declare const RiTranslate: IconType;
export declare const RiUnderline: IconType;
export declare const RiWubiInput: IconType;
export declare const Ri24HoursFill: IconType;
export declare const Ri24HoursLine: IconType;
export declare const RiAuctionFill: IconType;
export declare const RiAuctionLine: IconType;
export declare const RiBankCard2Fill: IconType;
export declare const RiBankCard2Line: IconType;
export declare const RiBankCardFill: IconType;
export declare const RiBankCardLine: IconType;
export declare const RiBitCoinFill: IconType;
export declare const RiBitCoinLine: IconType;
export declare const RiBnbFill: IconType;
export declare const RiBnbLine: IconType;
export declare const RiBtcFill: IconType;
export declare const RiBtcLine: IconType;
export declare const RiCashFill: IconType;
export declare const RiCashLine: IconType;
export declare const RiCoinFill: IconType;
export declare const RiCoinLine: IconType;
export declare const RiCoinsFill: IconType;
export declare const RiCoinsLine: IconType;
export declare const RiCopperCoinFill: IconType;
export declare const RiCopperCoinLine: IconType;
export declare const RiCopperDiamondFill: IconType;
export declare const RiCopperDiamondLine: IconType;
export declare const RiCoupon2Fill: IconType;
export declare const RiCoupon2Line: IconType;
export declare const RiCoupon3Fill: IconType;
export declare const RiCoupon3Line: IconType;
export declare const RiCoupon4Fill: IconType;
export declare const RiCoupon4Line: IconType;
export declare const RiCoupon5Fill: IconType;
export declare const RiCoupon5Line: IconType;
export declare const RiCouponFill: IconType;
export declare const RiCouponLine: IconType;
export declare const RiCurrencyFill: IconType;
export declare const RiCurrencyLine: IconType;
export declare const RiDiscountPercentFill: IconType;
export declare const RiDiscountPercentLine: IconType;
export declare const RiEthFill: IconType;
export declare const RiEthLine: IconType;
export declare const RiExchange2Fill: IconType;
export declare const RiExchange2Line: IconType;
export declare const RiExchangeBoxFill: IconType;
export declare const RiExchangeBoxLine: IconType;
export declare const RiExchangeCnyFill: IconType;
export declare const RiExchangeCnyLine: IconType;
export declare const RiExchangeDollarFill: IconType;
export declare const RiExchangeDollarLine: IconType;
export declare const RiExchangeFill: IconType;
export declare const RiExchangeFundsFill: IconType;
export declare const RiExchangeFundsLine: IconType;
export declare const RiExchangeLine: IconType;
export declare const RiFundsBoxFill: IconType;
export declare const RiFundsBoxLine: IconType;
export declare const RiFundsFill: IconType;
export declare const RiFundsLine: IconType;
export declare const RiGift2Fill: IconType;
export declare const RiGift2Line: IconType;
export declare const RiGiftFill: IconType;
export declare const RiGiftLine: IconType;
export declare const RiHandCoinFill: IconType;
export declare const RiHandCoinLine: IconType;
export declare const RiHandHeartFill: IconType;
export declare const RiHandHeartLine: IconType;
export declare const RiIncreaseDecreaseFill: IconType;
export declare const RiIncreaseDecreaseLine: IconType;
export declare const RiMoneyCnyBoxFill: IconType;
export declare const RiMoneyCnyBoxLine: IconType;
export declare const RiMoneyCnyCircleFill: IconType;
export declare const RiMoneyCnyCircleLine: IconType;
export declare const RiMoneyDollarBoxFill: IconType;
export declare const RiMoneyDollarBoxLine: IconType;
export declare const RiMoneyDollarCircleFill: IconType;
export declare const RiMoneyDollarCircleLine: IconType;
export declare const RiMoneyEuroBoxFill: IconType;
export declare const RiMoneyEuroBoxLine: IconType;
export declare const RiMoneyEuroCircleFill: IconType;
export declare const RiMoneyEuroCircleLine: IconType;
export declare const RiMoneyPoundBoxFill: IconType;
export declare const RiMoneyPoundBoxLine: IconType;
export declare const RiMoneyPoundCircleFill: IconType;
export declare const RiMoneyPoundCircleLine: IconType;
export declare const RiMoneyRupeeCircleFill: IconType;
export declare const RiMoneyRupeeCircleLine: IconType;
export declare const RiNftFill: IconType;
export declare const RiNftLine: IconType;
export declare const RiP2pFill: IconType;
export declare const RiP2pLine: IconType;
export declare const RiPercentFill: IconType;
export declare const RiPercentLine: IconType;
export declare const RiPriceTag2Fill: IconType;
export declare const RiPriceTag2Line: IconType;
export declare const RiPriceTag3Fill: IconType;
export declare const RiPriceTag3Line: IconType;
export declare const RiPriceTagFill: IconType;
export declare const RiPriceTagLine: IconType;
export declare const RiRedPacketFill: IconType;
export declare const RiRedPacketLine: IconType;
export declare const RiRefund2Fill: IconType;
export declare const RiRefund2Line: IconType;
export declare const RiRefundFill: IconType;
export declare const RiRefundLine: IconType;
export declare const RiSafe2Fill: IconType;
export declare const RiSafe2Line: IconType;
export declare const RiSafe3Fill: IconType;
export declare const RiSafe3Line: IconType;
export declare const RiSafeFill: IconType;
export declare const RiSafeLine: IconType;
export declare const RiSecurePaymentFill: IconType;
export declare const RiSecurePaymentLine: IconType;
export declare const RiShoppingBag2Fill: IconType;
export declare const RiShoppingBag2Line: IconType;
export declare const RiShoppingBag3Fill: IconType;
export declare const RiShoppingBag3Line: IconType;
export declare const RiShoppingBag4Fill: IconType;
export declare const RiShoppingBag4Line: IconType;
export declare const RiShoppingBagFill: IconType;
export declare const RiShoppingBagLine: IconType;
export declare const RiShoppingBasket2Fill: IconType;
export declare const RiShoppingBasket2Line: IconType;
export declare const RiShoppingBasketFill: IconType;
export declare const RiShoppingBasketLine: IconType;
export declare const RiShoppingCart2Fill: IconType;
export declare const RiShoppingCart2Line: IconType;
export declare const RiShoppingCartFill: IconType;
export declare const RiShoppingCartLine: IconType;
export declare const RiStockFill: IconType;
export declare const RiStockLine: IconType;
export declare const RiSwap2Fill: IconType;
export declare const RiSwap2Line: IconType;
export declare const RiSwap3Fill: IconType;
export declare const RiSwap3Line: IconType;
export declare const RiSwapBoxFill: IconType;
export declare const RiSwapBoxLine: IconType;
export declare const RiSwapFill: IconType;
export declare const RiSwapLine: IconType;
export declare const RiTicket2Fill: IconType;
export declare const RiTicket2Line: IconType;
export declare const RiTicketFill: IconType;
export declare const RiTicketLine: IconType;
export declare const RiTokenSwapFill: IconType;
export declare const RiTokenSwapLine: IconType;
export declare const RiTrophyFill: IconType;
export declare const RiTrophyLine: IconType;
export declare const RiVipCrown2Fill: IconType;
export declare const RiVipCrown2Line: IconType;
export declare const RiVipCrownFill: IconType;
export declare const RiVipCrownLine: IconType;
export declare const RiVipDiamondFill: IconType;
export declare const RiVipDiamondLine: IconType;
export declare const RiVipFill: IconType;
export declare const RiVipLine: IconType;
export declare const RiWallet2Fill: IconType;
export declare const RiWallet2Line: IconType;
export declare const RiWallet3Fill: IconType;
export declare const RiWallet3Line: IconType;
export declare const RiWalletFill: IconType;
export declare const RiWalletLine: IconType;
export declare const RiWaterFlashFill: IconType;
export declare const RiWaterFlashLine: IconType;
export declare const RiXrpFill: IconType;
export declare const RiXrpLine: IconType;
export declare const RiXtzFill: IconType;
export declare const RiXtzLine: IconType;
export declare const RiBeerFill: IconType;
export declare const RiBeerLine: IconType;
export declare const RiBowlFill: IconType;
export declare const RiBowlLine: IconType;
export declare const RiBreadFill: IconType;
export declare const RiBreadLine: IconType;
export declare const RiCake2Fill: IconType;
export declare const RiCake2Line: IconType;
export declare const RiCake3Fill: IconType;
export declare const RiCake3Line: IconType;
export declare const RiCakeFill: IconType;
export declare const RiCakeLine: IconType;
export declare const RiCupFill: IconType;
export declare const RiCupLine: IconType;
export declare const RiDrinks2Fill: IconType;
export declare const RiDrinks2Line: IconType;
export declare const RiDrinksFill: IconType;
export declare const RiDrinksLine: IconType;
export declare const RiGoblet2Fill: IconType;
export declare const RiGoblet2Line: IconType;
export declare const RiGobletFill: IconType;
export declare const RiGobletLine: IconType;
export declare const RiKnifeBloodFill: IconType;
export declare const RiKnifeBloodLine: IconType;
export declare const RiKnifeFill: IconType;
export declare const RiKnifeLine: IconType;
export declare const RiRestaurant2Fill: IconType;
export declare const RiRestaurant2Line: IconType;
export declare const RiRestaurantFill: IconType;
export declare const RiRestaurantLine: IconType;
export declare const RiAedElectrodesFill: IconType;
export declare const RiAedElectrodesLine: IconType;
export declare const RiAedFill: IconType;
export declare const RiAedLine: IconType;
export declare const RiBrain2Fill: IconType;
export declare const RiBrain2Line: IconType;
export declare const RiBrainFill: IconType;
export declare const RiBrainLine: IconType;
export declare const RiCapsuleFill: IconType;
export declare const RiCapsuleLine: IconType;
export declare const RiDislikeFill: IconType;
export declare const RiDislikeLine: IconType;
export declare const RiDnaFill: IconType;
export declare const RiDnaLine: IconType;
export declare const RiDossierFill: IconType;
export declare const RiDossierLine: IconType;
export declare const RiDropperFill: IconType;
export declare const RiDropperLine: IconType;
export declare const RiEmpathizeFill: IconType;
export declare const RiEmpathizeLine: IconType;
export declare const RiFirstAidKitFill: IconType;
export declare const RiFirstAidKitLine: IconType;
export declare const RiFlaskFill: IconType;
export declare const RiFlaskLine: IconType;
export declare const RiHandSanitizerFill: IconType;
export declare const RiHandSanitizerLine: IconType;
export declare const RiHealthBookFill: IconType;
export declare const RiHealthBookLine: IconType;
export declare const RiHeart2Fill: IconType;
export declare const RiHeart2Line: IconType;
export declare const RiHeart3Fill: IconType;
export declare const RiHeart3Line: IconType;
export declare const RiHeartAdd2Fill: IconType;
export declare const RiHeartAdd2Line: IconType;
export declare const RiHeartAddFill: IconType;
export declare const RiHeartAddLine: IconType;
export declare const RiHeartFill: IconType;
export declare const RiHeartLine: IconType;
export declare const RiHeartPulseFill: IconType;
export declare const RiHeartPulseLine: IconType;
export declare const RiHeartsFill: IconType;
export declare const RiHeartsLine: IconType;
export declare const RiInfraredThermometerFill: IconType;
export declare const RiInfraredThermometerLine: IconType;
export declare const RiLungsFill: IconType;
export declare const RiLungsLine: IconType;
export declare const RiMedicineBottleFill: IconType;
export declare const RiMedicineBottleLine: IconType;
export declare const RiMentalHealthFill: IconType;
export declare const RiMentalHealthLine: IconType;
export declare const RiMicroscopeFill: IconType;
export declare const RiMicroscopeLine: IconType;
export declare const RiNurseFill: IconType;
export declare const RiNurseLine: IconType;
export declare const RiPsychotherapyFill: IconType;
export declare const RiPsychotherapyLine: IconType;
export declare const RiPulseAiFill: IconType;
export declare const RiPulseAiLine: IconType;
export declare const RiPulseFill: IconType;
export declare const RiPulseLine: IconType;
export declare const RiRestTimeFill: IconType;
export declare const RiRestTimeLine: IconType;
export declare const RiStethoscopeFill: IconType;
export declare const RiStethoscopeLine: IconType;
export declare const RiSurgicalMaskFill: IconType;
export declare const RiSurgicalMaskLine: IconType;
export declare const RiSyringeFill: IconType;
export declare const RiSyringeLine: IconType;
export declare const RiTestTubeFill: IconType;
export declare const RiTestTubeLine: IconType;
export declare const RiThermometerFill: IconType;
export declare const RiThermometerLine: IconType;
export declare const RiVirusFill: IconType;
export declare const RiVirusLine: IconType;
export declare const RiZzzFill: IconType;
export declare const RiZzzLine: IconType;
export declare const RiAlibabaCloudFill: IconType;
export declare const RiAlibabaCloudLine: IconType;
export declare const RiAlipayFill: IconType;
export declare const RiAlipayLine: IconType;
export declare const RiAmazonFill: IconType;
export declare const RiAmazonLine: IconType;
export declare const RiAndroidFill: IconType;
export declare const RiAndroidLine: IconType;
export declare const RiAngularjsFill: IconType;
export declare const RiAngularjsLine: IconType;
export declare const RiAnthropicFill: IconType;
export declare const RiAnthropicLine: IconType;
export declare const RiAppStoreFill: IconType;
export declare const RiAppStoreLine: IconType;
export declare const RiAppleFill: IconType;
export declare const RiAppleLine: IconType;
export declare const RiBaiduFill: IconType;
export declare const RiBaiduLine: IconType;
export declare const RiBardFill: IconType;
export declare const RiBardLine: IconType;
export declare const RiBehanceFill: IconType;
export declare const RiBehanceLine: IconType;
export declare const RiBilibiliFill: IconType;
export declare const RiBilibiliLine: IconType;
export declare const RiBlenderFill: IconType;
export declare const RiBlenderLine: IconType;
export declare const RiBloggerFill: IconType;
export declare const RiBloggerLine: IconType;
export declare const RiBlueskyFill: IconType;
export declare const RiBlueskyLine: IconType;
export declare const RiBootstrapFill: IconType;
export declare const RiBootstrapLine: IconType;
export declare const RiCentosFill: IconType;
export declare const RiCentosLine: IconType;
export declare const RiChromeFill: IconType;
export declare const RiChromeLine: IconType;
export declare const RiClaudeFill: IconType;
export declare const RiClaudeLine: IconType;
export declare const RiCodepenFill: IconType;
export declare const RiCodepenLine: IconType;
export declare const RiCopilotFill: IconType;
export declare const RiCopilotLine: IconType;
export declare const RiCoreosFill: IconType;
export declare const RiCoreosLine: IconType;
export declare const RiDingdingFill: IconType;
export declare const RiDingdingLine: IconType;
export declare const RiDiscordFill: IconType;
export declare const RiDiscordLine: IconType;
export declare const RiDisqusFill: IconType;
export declare const RiDisqusLine: IconType;
export declare const RiDoubanFill: IconType;
export declare const RiDoubanLine: IconType;
export declare const RiDribbbleFill: IconType;
export declare const RiDribbbleLine: IconType;
export declare const RiDriveFill: IconType;
export declare const RiDriveLine: IconType;
export declare const RiDropboxFill: IconType;
export declare const RiDropboxLine: IconType;
export declare const RiEdgeFill: IconType;
export declare const RiEdgeLine: IconType;
export declare const RiEdgeNewFill: IconType;
export declare const RiEdgeNewLine: IconType;
export declare const RiEvernoteFill: IconType;
export declare const RiEvernoteLine: IconType;
export declare const RiFacebookBoxFill: IconType;
export declare const RiFacebookBoxLine: IconType;
export declare const RiFacebookCircleFill: IconType;
export declare const RiFacebookCircleLine: IconType;
export declare const RiFacebookFill: IconType;
export declare const RiFacebookLine: IconType;
export declare const RiFediverseFill: IconType;
export declare const RiFediverseLine: IconType;
export declare const RiFinderFill: IconType;
export declare const RiFinderLine: IconType;
export declare const RiFirebaseFill: IconType;
export declare const RiFirebaseLine: IconType;
export declare const RiFirefoxFill: IconType;
export declare const RiFirefoxLine: IconType;
export declare const RiFlickrFill: IconType;
export declare const RiFlickrLine: IconType;
export declare const RiFlutterFill: IconType;
export declare const RiFlutterLine: IconType;
export declare const RiFriendicaFill: IconType;
export declare const RiFriendicaLine: IconType;
export declare const RiGatsbyFill: IconType;
export declare const RiGatsbyLine: IconType;
export declare const RiGeminiFill: IconType;
export declare const RiGeminiLine: IconType;
export declare const RiGithubFill: IconType;
export declare const RiGithubLine: IconType;
export declare const RiGitlabFill: IconType;
export declare const RiGitlabLine: IconType;
export declare const RiGoogleFill: IconType;
export declare const RiGoogleLine: IconType;
export declare const RiGooglePlayFill: IconType;
export declare const RiGooglePlayLine: IconType;
export declare const RiHonorOfKingsFill: IconType;
export declare const RiHonorOfKingsLine: IconType;
export declare const RiIeFill: IconType;
export declare const RiIeLine: IconType;
export declare const RiInstagramFill: IconType;
export declare const RiInstagramLine: IconType;
export declare const RiInvisionFill: IconType;
export declare const RiInvisionLine: IconType;
export declare const RiJavaFill: IconType;
export declare const RiJavaLine: IconType;
export declare const RiKakaoTalkFill: IconType;
export declare const RiKakaoTalkLine: IconType;
export declare const RiKickFill: IconType;
export declare const RiKickLine: IconType;
export declare const RiLineFill: IconType;
export declare const RiLineLine: IconType;
export declare const RiLinkedinBoxFill: IconType;
export declare const RiLinkedinBoxLine: IconType;
export declare const RiLinkedinFill: IconType;
export declare const RiLinkedinLine: IconType;
export declare const RiMastercardFill: IconType;
export declare const RiMastercardLine: IconType;
export declare const RiMastodonFill: IconType;
export declare const RiMastodonLine: IconType;
export declare const RiMediumFill: IconType;
export declare const RiMediumLine: IconType;
export declare const RiMessengerFill: IconType;
export declare const RiMessengerLine: IconType;
export declare const RiMetaFill: IconType;
export declare const RiMetaLine: IconType;
export declare const RiMicrosoftFill: IconType;
export declare const RiMicrosoftLine: IconType;
export declare const RiMicrosoftLoopFill: IconType;
export declare const RiMicrosoftLoopLine: IconType;
export declare const RiMiniProgramFill: IconType;
export declare const RiMiniProgramLine: IconType;
export declare const RiMixtralFill: IconType;
export declare const RiMixtralLine: IconType;
export declare const RiNeteaseCloudMusicFill: IconType;
export declare const RiNeteaseCloudMusicLine: IconType;
export declare const RiNetflixFill: IconType;
export declare const RiNetflixLine: IconType;
export declare const RiNextjsFill: IconType;
export declare const RiNextjsLine: IconType;
export declare const RiNodejsFill: IconType;
export declare const RiNodejsLine: IconType;
export declare const RiNotionFill: IconType;
export declare const RiNotionLine: IconType;
export declare const RiNpmjsFill: IconType;
export declare const RiNpmjsLine: IconType;
export declare const RiOpenSourceFill: IconType;
export declare const RiOpenSourceLine: IconType;
export declare const RiOpenaiFill: IconType;
export declare const RiOpenaiLine: IconType;
export declare const RiOpenbaseFill: IconType;
export declare const RiOpenbaseLine: IconType;
export declare const RiOperaFill: IconType;
export declare const RiOperaLine: IconType;
export declare const RiPatreonFill: IconType;
export declare const RiPatreonLine: IconType;
export declare const RiPaypalFill: IconType;
export declare const RiPaypalLine: IconType;
export declare const RiPerplexityFill: IconType;
export declare const RiPerplexityLine: IconType;
export declare const RiPinterestFill: IconType;
export declare const RiPinterestLine: IconType;
export declare const RiPixFill: IconType;
export declare const RiPixLine: IconType;
export declare const RiPixelfedFill: IconType;
export declare const RiPixelfedLine: IconType;
export declare const RiPlaystationFill: IconType;
export declare const RiPlaystationLine: IconType;
export declare const RiProductHuntFill: IconType;
export declare const RiProductHuntLine: IconType;
export declare const RiQqFill: IconType;
export declare const RiQqLine: IconType;
export declare const RiReactjsFill: IconType;
export declare const RiReactjsLine: IconType;
export declare const RiRedditFill: IconType;
export declare const RiRedditLine: IconType;
export declare const RiRemixRunFill: IconType;
export declare const RiRemixRunLine: IconType;
export declare const RiRemixiconFill: IconType;
export declare const RiRemixiconLine: IconType;
export declare const RiSafariFill: IconType;
export declare const RiSafariLine: IconType;
export declare const RiSkypeFill: IconType;
export declare const RiSkypeLine: IconType;
export declare const RiSlackFill: IconType;
export declare const RiSlackLine: IconType;
export declare const RiSnapchatFill: IconType;
export declare const RiSnapchatLine: IconType;
export declare const RiSoundcloudFill: IconType;
export declare const RiSoundcloudLine: IconType;
export declare const RiSpectrumFill: IconType;
export declare const RiSpectrumLine: IconType;
export declare const RiSpotifyFill: IconType;
export declare const RiSpotifyLine: IconType;
export declare const RiStackOverflowFill: IconType;
export declare const RiStackOverflowLine: IconType;
export declare const RiStackshareFill: IconType;
export declare const RiStackshareLine: IconType;
export declare const RiSteamFill: IconType;
export declare const RiSteamLine: IconType;
export declare const RiSupabaseFill: IconType;
export declare const RiSupabaseLine: IconType;
export declare const RiSvelteFill: IconType;
export declare const RiSvelteLine: IconType;
export declare const RiSwitchFill: IconType;
export declare const RiSwitchLine: IconType;
export declare const RiTailwindCssFill: IconType;
export declare const RiTailwindCssLine: IconType;
export declare const RiTaobaoFill: IconType;
export declare const RiTaobaoLine: IconType;
export declare const RiTelegram2Fill: IconType;
export declare const RiTelegram2Line: IconType;
export declare const RiTelegramFill: IconType;
export declare const RiTelegramLine: IconType;
export declare const RiThreadsFill: IconType;
export declare const RiThreadsLine: IconType;
export declare const RiTiktokFill: IconType;
export declare const RiTiktokLine: IconType;
export declare const RiTrelloFill: IconType;
export declare const RiTrelloLine: IconType;
export declare const RiTumblrFill: IconType;
export declare const RiTumblrLine: IconType;
export declare const RiTwitchFill: IconType;
export declare const RiTwitchLine: IconType;
export declare const RiTwitterFill: IconType;
export declare const RiTwitterLine: IconType;
export declare const RiTwitterXFill: IconType;
export declare const RiTwitterXLine: IconType;
export declare const RiUbuntuFill: IconType;
export declare const RiUbuntuLine: IconType;
export declare const RiUnsplashFill: IconType;
export declare const RiUnsplashLine: IconType;
export declare const RiVercelFill: IconType;
export declare const RiVercelLine: IconType;
export declare const RiVimeoFill: IconType;
export declare const RiVimeoLine: IconType;
export declare const RiVisaFill: IconType;
export declare const RiVisaLine: IconType;
export declare const RiVkFill: IconType;
export declare const RiVkLine: IconType;
export declare const RiVuejsFill: IconType;
export declare const RiVuejsLine: IconType;
export declare const RiWebhookFill: IconType;
export declare const RiWebhookLine: IconType;
export declare const RiWechat2Fill: IconType;
export declare const RiWechat2Line: IconType;
export declare const RiWechatChannelsFill: IconType;
export declare const RiWechatChannelsLine: IconType;
export declare const RiWechatFill: IconType;
export declare const RiWechatLine: IconType;
export declare const RiWechatPayFill: IconType;
export declare const RiWechatPayLine: IconType;
export declare const RiWeiboFill: IconType;
export declare const RiWeiboLine: IconType;
export declare const RiWhatsappFill: IconType;
export declare const RiWhatsappLine: IconType;
export declare const RiWindowsFill: IconType;
export declare const RiWindowsLine: IconType;
export declare const RiWordpressFill: IconType;
export declare const RiWordpressLine: IconType;
export declare const RiXboxFill: IconType;
export declare const RiXboxLine: IconType;
export declare const RiXingFill: IconType;
export declare const RiXingLine: IconType;
export declare const RiYoutubeFill: IconType;
export declare const RiYoutubeLine: IconType;
export declare const RiYuqueFill: IconType;
export declare const RiYuqueLine: IconType;
export declare const RiZcoolFill: IconType;
export declare const RiZcoolLine: IconType;
export declare const RiZhihuFill: IconType;
export declare const RiZhihuLine: IconType;
export declare const RiAnchorFill: IconType;
export declare const RiAnchorLine: IconType;
export declare const RiBarricadeFill: IconType;
export declare const RiBarricadeLine: IconType;
export declare const RiBikeFill: IconType;
export declare const RiBikeLine: IconType;
export declare const RiBus2Fill: IconType;
export declare const RiBus2Line: IconType;
export declare const RiBusFill: IconType;
export declare const RiBusLine: IconType;
export declare const RiBusWifiFill: IconType;
export declare const RiBusWifiLine: IconType;
export declare const RiCarFill: IconType;
export declare const RiCarLine: IconType;
export declare const RiCarWashingFill: IconType;
export declare const RiCarWashingLine: IconType;
export declare const RiCaravanFill: IconType;
export declare const RiCaravanLine: IconType;
export declare const RiChargingPile2Fill: IconType;
export declare const RiChargingPile2Line: IconType;
export declare const RiChargingPileFill: IconType;
export declare const RiChargingPileLine: IconType;
export declare const RiChinaRailwayFill: IconType;
export declare const RiChinaRailwayLine: IconType;
export declare const RiCompass2Fill: IconType;
export declare const RiCompass2Line: IconType;
export declare const RiCompass3Fill: IconType;
export declare const RiCompass3Line: IconType;
export declare const RiCompass4Fill: IconType;
export declare const RiCompass4Line: IconType;
export declare const RiCompassDiscoverFill: IconType;
export declare const RiCompassDiscoverLine: IconType;
export declare const RiCompassFill: IconType;
export declare const RiCompassLine: IconType;
export declare const RiDirectionFill: IconType;
export declare const RiDirectionLine: IconType;
export declare const RiEBike2Fill: IconType;
export declare const RiEBike2Line: IconType;
export declare const RiEBikeFill: IconType;
export declare const RiEBikeLine: IconType;
export declare const RiEarthFill: IconType;
export declare const RiEarthLine: IconType;
export declare const RiFlightLandFill: IconType;
export declare const RiFlightLandLine: IconType;
export declare const RiFlightTakeoffFill: IconType;
export declare const RiFlightTakeoffLine: IconType;
export declare const RiFootprintFill: IconType;
export declare const RiFootprintLine: IconType;
export declare const RiGasStationFill: IconType;
export declare const RiGasStationLine: IconType;
export declare const RiGlobeFill: IconType;
export declare const RiGlobeLine: IconType;
export declare const RiGuideFill: IconType;
export declare const RiGuideLine: IconType;
export declare const RiHotelBedFill: IconType;
export declare const RiHotelBedLine: IconType;
export declare const RiLifebuoyFill: IconType;
export declare const RiLifebuoyLine: IconType;
export declare const RiLuggageCartFill: IconType;
export declare const RiLuggageCartLine: IconType;
export declare const RiLuggageDepositFill: IconType;
export declare const RiLuggageDepositLine: IconType;
export declare const RiMap2Fill: IconType;
export declare const RiMap2Line: IconType;
export declare const RiMapFill: IconType;
export declare const RiMapLine: IconType;
export declare const RiMapPin2Fill: IconType;
export declare const RiMapPin2Line: IconType;
export declare const RiMapPin3Fill: IconType;
export declare const RiMapPin3Line: IconType;
export declare const RiMapPin4Fill: IconType;
export declare const RiMapPin4Line: IconType;
export declare const RiMapPin5Fill: IconType;
export declare const RiMapPin5Line: IconType;
export declare const RiMapPinAddFill: IconType;
export declare const RiMapPinAddLine: IconType;
export declare const RiMapPinFill: IconType;
export declare const RiMapPinLine: IconType;
export declare const RiMapPinRangeFill: IconType;
export declare const RiMapPinRangeLine: IconType;
export declare const RiMapPinTimeFill: IconType;
export declare const RiMapPinTimeLine: IconType;
export declare const RiMapPinUserFill: IconType;
export declare const RiMapPinUserLine: IconType;
export declare const RiMotorbikeFill: IconType;
export declare const RiMotorbikeLine: IconType;
export declare const RiNavigationFill: IconType;
export declare const RiNavigationLine: IconType;
export declare const RiOilFill: IconType;
export declare const RiOilLine: IconType;
export declare const RiParkingBoxFill: IconType;
export declare const RiParkingBoxLine: IconType;
export declare const RiParkingFill: IconType;
export declare const RiParkingLine: IconType;
export declare const RiPassportFill: IconType;
export declare const RiPassportLine: IconType;
export declare const RiPinDistanceFill: IconType;
export declare const RiPinDistanceLine: IconType;
export declare const RiPlaneFill: IconType;
export declare const RiPlaneLine: IconType;
export declare const RiPlanetFill: IconType;
export declare const RiPlanetLine: IconType;
export declare const RiPoliceCarFill: IconType;
export declare const RiPoliceCarLine: IconType;
export declare const RiPushpin2Fill: IconType;
export declare const RiPushpin2Line: IconType;
export declare const RiPushpinFill: IconType;
export declare const RiPushpinLine: IconType;
export declare const RiRidingFill: IconType;
export declare const RiRidingLine: IconType;
export declare const RiRoadMapFill: IconType;
export declare const RiRoadMapLine: IconType;
export declare const RiRoadsterFill: IconType;
export declare const RiRoadsterLine: IconType;
export declare const RiRocket2Fill: IconType;
export declare const RiRocket2Line: IconType;
export declare const RiRocketFill: IconType;
export declare const RiRocketLine: IconType;
export declare const RiRouteFill: IconType;
export declare const RiRouteLine: IconType;
export declare const RiRunFill: IconType;
export declare const RiRunLine: IconType;
export declare const RiSailboatFill: IconType;
export declare const RiSailboatLine: IconType;
export declare const RiShip2Fill: IconType;
export declare const RiShip2Line: IconType;
export declare const RiShipFill: IconType;
export declare const RiShipLine: IconType;
export declare const RiSignalTowerFill: IconType;
export declare const RiSignalTowerLine: IconType;
export declare const RiSignpostFill: IconType;
export declare const RiSignpostLine: IconType;
export declare const RiSpaceShipFill: IconType;
export declare const RiSpaceShipLine: IconType;
export declare const RiSteering2Fill: IconType;
export declare const RiSteering2Line: IconType;
export declare const RiSteeringFill: IconType;
export declare const RiSteeringLine: IconType;
export declare const RiSubwayFill: IconType;
export declare const RiSubwayLine: IconType;
export declare const RiSubwayWifiFill: IconType;
export declare const RiSubwayWifiLine: IconType;
export declare const RiSuitcase2Fill: IconType;
export declare const RiSuitcase2Line: IconType;
export declare const RiSuitcase3Fill: IconType;
export declare const RiSuitcase3Line: IconType;
export declare const RiSuitcaseFill: IconType;
export declare const RiSuitcaseLine: IconType;
export declare const RiTakeawayFill: IconType;
export declare const RiTakeawayLine: IconType;
export declare const RiTaxiFill: IconType;
export declare const RiTaxiLine: IconType;
export declare const RiTaxiWifiFill: IconType;
export declare const RiTaxiWifiLine: IconType;
export declare const RiTimeZoneFill: IconType;
export declare const RiTimeZoneLine: IconType;
export declare const RiTrafficLightFill: IconType;
export declare const RiTrafficLightLine: IconType;
export declare const RiTrainFill: IconType;
export declare const RiTrainLine: IconType;
export declare const RiTrainWifiFill: IconType;
export declare const RiTrainWifiLine: IconType;
export declare const RiTreasureMapFill: IconType;
export declare const RiTreasureMapLine: IconType;
export declare const RiTruckFill: IconType;
export declare const RiTruckLine: IconType;
export declare const RiUnpinFill: IconType;
export declare const RiUnpinLine: IconType;
export declare const RiWalkFill: IconType;
export declare const RiWalkLine: IconType;
export declare const Ri4kFill: IconType;
export declare const Ri4kLine: IconType;
export declare const RiAlbumFill: IconType;
export declare const RiAlbumLine: IconType;
export declare const RiAspectRatioFill: IconType;
export declare const RiAspectRatioLine: IconType;
export declare const RiBroadcastFill: IconType;
export declare const RiBroadcastLine: IconType;
export declare const RiCamera2Fill: IconType;
export declare const RiCamera2Line: IconType;
export declare const RiCamera3Fill: IconType;
export declare const RiCamera3Line: IconType;
export declare const RiCameraAiFill: IconType;
export declare const RiCameraAiLine: IconType;
export declare const RiCameraFill: IconType;
export declare const RiCameraLensAiFill: IconType;
export declare const RiCameraLensAiLine: IconType;
export declare const RiCameraLensFill: IconType;
export declare const RiCameraLensLine: IconType;
export declare const RiCameraLine: IconType;
export declare const RiCameraOffFill: IconType;
export declare const RiCameraOffLine: IconType;
export declare const RiCameraSwitchFill: IconType;
export declare const RiCameraSwitchLine: IconType;
export declare const RiClapperboardAiFill: IconType;
export declare const RiClapperboardAiLine: IconType;
export declare const RiClapperboardFill: IconType;
export declare const RiClapperboardLine: IconType;
export declare const RiClosedCaptioningAiFill: IconType;
export declare const RiClosedCaptioningAiLine: IconType;
export declare const RiClosedCaptioningFill: IconType;
export declare const RiClosedCaptioningLine: IconType;
export declare const RiDiscFill: IconType;
export declare const RiDiscLine: IconType;
export declare const RiDvFill: IconType;
export declare const RiDvLine: IconType;
export declare const RiDvdAiFill: IconType;
export declare const RiDvdAiLine: IconType;
export declare const RiDvdFill: IconType;
export declare const RiDvdLine: IconType;
export declare const RiEjectFill: IconType;
export declare const RiEjectLine: IconType;
export declare const RiEqualizer2Fill: IconType;
export declare const RiEqualizer2Line: IconType;
export declare const RiEqualizer3Fill: IconType;
export declare const RiEqualizer3Line: IconType;
export declare const RiEqualizerFill: IconType;
export declare const RiEqualizerLine: IconType;
export declare const RiFilmAiFill: IconType;
export declare const RiFilmAiLine: IconType;
export declare const RiFilmFill: IconType;
export declare const RiFilmLine: IconType;
export declare const RiForward10Fill: IconType;
export declare const RiForward10Line: IconType;
export declare const RiForward15Fill: IconType;
export declare const RiForward15Line: IconType;
export declare const RiForward30Fill: IconType;
export declare const RiForward30Line: IconType;
export declare const RiForward5Fill: IconType;
export declare const RiForward5Line: IconType;
export declare const RiForwardEndFill: IconType;
export declare const RiForwardEndLine: IconType;
export declare const RiForwardEndMiniFill: IconType;
export declare const RiForwardEndMiniLine: IconType;
export declare const RiFullscreenExitFill: IconType;
export declare const RiFullscreenExitLine: IconType;
export declare const RiFullscreenFill: IconType;
export declare const RiFullscreenLine: IconType;
export declare const RiGalleryFill: IconType;
export declare const RiGalleryLine: IconType;
export declare const RiGalleryUploadFill: IconType;
export declare const RiGalleryUploadLine: IconType;
export declare const RiHdFill: IconType;
export declare const RiHdLine: IconType;
export declare const RiHeadphoneFill: IconType;
export declare const RiHeadphoneLine: IconType;
export declare const RiHqFill: IconType;
export declare const RiHqLine: IconType;
export declare const RiImage2Fill: IconType;
export declare const RiImage2Line: IconType;
export declare const RiImageAddFill: IconType;
export declare const RiImageAddLine: IconType;
export declare const RiImageAiFill: IconType;
export declare const RiImageAiLine: IconType;
export declare const RiImageCircleAiFill: IconType;
export declare const RiImageCircleAiLine: IconType;
export declare const RiImageCircleFill: IconType;
export declare const RiImageCircleLine: IconType;
export declare const RiImageEditFill: IconType;
export declare const RiImageEditLine: IconType;
export declare const RiImageFill: IconType;
export declare const RiImageLine: IconType;
export declare const RiLandscapeAiFill: IconType;
export declare const RiLandscapeAiLine: IconType;
export declare const RiLandscapeFill: IconType;
export declare const RiLandscapeLine: IconType;
export declare const RiLiveFill: IconType;
export declare const RiLiveLine: IconType;
export declare const RiMemoriesFill: IconType;
export declare const RiMemoriesLine: IconType;
export declare const RiMic2AiFill: IconType;
export declare const RiMic2AiLine: IconType;
export declare const RiMic2Fill: IconType;
export declare const RiMic2Line: IconType;
export declare const RiMicAiFill: IconType;
export declare const RiMicAiLine: IconType;
export declare const RiMicFill: IconType;
export declare const RiMicLine: IconType;
export declare const RiMicOffFill: IconType;
export declare const RiMicOffLine: IconType;
export declare const RiMovie2AiFill: IconType;
export declare const RiMovie2AiLine: IconType;
export declare const RiMovie2Fill: IconType;
export declare const RiMovie2Line: IconType;
export declare const RiMovieAiFill: IconType;
export declare const RiMovieAiLine: IconType;
export declare const RiMovieFill: IconType;
export declare const RiMovieLine: IconType;
export declare const RiMusic2Fill: IconType;
export declare const RiMusic2Line: IconType;
export declare const RiMusicAiFill: IconType;
export declare const RiMusicAiLine: IconType;
export declare const RiMusicFill: IconType;
export declare const RiMusicLine: IconType;
export declare const RiMvAiFill: IconType;
export declare const RiMvAiLine: IconType;
export declare const RiMvFill: IconType;
export declare const RiMvLine: IconType;
export declare const RiNotification2Fill: IconType;
export declare const RiNotification2Line: IconType;
export declare const RiNotification3Fill: IconType;
export declare const RiNotification3Line: IconType;
export declare const RiNotification4Fill: IconType;
export declare const RiNotification4Line: IconType;
export declare const RiNotificationFill: IconType;
export declare const RiNotificationLine: IconType;
export declare const RiNotificationOffFill: IconType;
export declare const RiNotificationOffLine: IconType;
export declare const RiNotificationSnoozeFill: IconType;
export declare const RiNotificationSnoozeLine: IconType;
export declare const RiOrderPlayFill: IconType;
export declare const RiOrderPlayLine: IconType;
export declare const RiPauseCircleFill: IconType;
export declare const RiPauseCircleLine: IconType;
export declare const RiPauseFill: IconType;
export declare const RiPauseLargeFill: IconType;
export declare const RiPauseLargeLine: IconType;
export declare const RiPauseLine: IconType;
export declare const RiPauseMiniFill: IconType;
export declare const RiPauseMiniLine: IconType;
export declare const RiPhoneCameraFill: IconType;
export declare const RiPhoneCameraLine: IconType;
export declare const RiPictureInPicture2Fill: IconType;
export declare const RiPictureInPicture2Line: IconType;
export declare const RiPictureInPictureExitFill: IconType;
export declare const RiPictureInPictureExitLine: IconType;
export declare const RiPictureInPictureFill: IconType;
export declare const RiPictureInPictureLine: IconType;
export declare const RiPlayCircleFill: IconType;
export declare const RiPlayCircleLine: IconType;
export declare const RiPlayFill: IconType;
export declare const RiPlayLargeFill: IconType;
export declare const RiPlayLargeLine: IconType;
export declare const RiPlayLine: IconType;
export declare const RiPlayList2Fill: IconType;
export declare const RiPlayList2Line: IconType;
export declare const RiPlayListAddFill: IconType;
export declare const RiPlayListAddLine: IconType;
export declare const RiPlayListFill: IconType;
export declare const RiPlayListLine: IconType;
export declare const RiPlayMiniFill: IconType;
export declare const RiPlayMiniLine: IconType;
export declare const RiPlayReverseFill: IconType;
export declare const RiPlayReverseLargeFill: IconType;
export declare const RiPlayReverseLargeLine: IconType;
export declare const RiPlayReverseLine: IconType;
export declare const RiPlayReverseMiniFill: IconType;
export declare const RiPlayReverseMiniLine: IconType;
export declare const RiPolaroid2Fill: IconType;
export declare const RiPolaroid2Line: IconType;
export declare const RiPolaroidFill: IconType;
export declare const RiPolaroidLine: IconType;
export declare const RiRadio2Fill: IconType;
export declare const RiRadio2Line: IconType;
export declare const RiRadioFill: IconType;
export declare const RiRadioLine: IconType;
export declare const RiRecordCircleFill: IconType;
export declare const RiRecordCircleLine: IconType;
export declare const RiRepeat2Fill: IconType;
export declare const RiRepeat2Line: IconType;
export declare const RiRepeatFill: IconType;
export declare const RiRepeatLine: IconType;
export declare const RiRepeatOneFill: IconType;
export declare const RiRepeatOneLine: IconType;
export declare const RiReplay10Fill: IconType;
export declare const RiReplay10Line: IconType;
export declare const RiReplay15Fill: IconType;
export declare const RiReplay15Line: IconType;
export declare const RiReplay30Fill: IconType;
export declare const RiReplay30Line: IconType;
export declare const RiReplay5Fill: IconType;
export declare const RiReplay5Line: IconType;
export declare const RiRewindFill: IconType;
export declare const RiRewindLine: IconType;
export declare const RiRewindMiniFill: IconType;
export declare const RiRewindMiniLine: IconType;
export declare const RiRewindStartFill: IconType;
export declare const RiRewindStartLine: IconType;
export declare const RiRewindStartMiniFill: IconType;
export declare const RiRewindStartMiniLine: IconType;
export declare const RiRhythmFill: IconType;
export declare const RiRhythmLine: IconType;
export declare const RiShuffleFill: IconType;
export declare const RiShuffleLine: IconType;
export declare const RiSkipBackFill: IconType;
export declare const RiSkipBackLine: IconType;
export declare const RiSkipBackMiniFill: IconType;
export declare const RiSkipBackMiniLine: IconType;
export declare const RiSkipForwardFill: IconType;
export declare const RiSkipForwardLine: IconType;
export declare const RiSkipForwardMiniFill: IconType;
export declare const RiSkipForwardMiniLine: IconType;
export declare const RiSlowDownFill: IconType;
export declare const RiSlowDownLine: IconType;
export declare const RiSoundModuleFill: IconType;
export declare const RiSoundModuleLine: IconType;
export declare const RiSpeaker2Fill: IconType;
export declare const RiSpeaker2Line: IconType;
export declare const RiSpeaker3Fill: IconType;
export declare const RiSpeaker3Line: IconType;
export declare const RiSpeakerFill: IconType;
export declare const RiSpeakerLine: IconType;
export declare const RiSpeedFill: IconType;
export declare const RiSpeedLine: IconType;
export declare const RiSpeedMiniFill: IconType;
export declare const RiSpeedMiniLine: IconType;
export declare const RiSpeedUpFill: IconType;
export declare const RiSpeedUpLine: IconType;
export declare const RiStopCircleFill: IconType;
export declare const RiStopCircleLine: IconType;
export declare const RiStopFill: IconType;
export declare const RiStopLargeFill: IconType;
export declare const RiStopLargeLine: IconType;
export declare const RiStopLine: IconType;
export declare const RiStopMiniFill: IconType;
export declare const RiStopMiniLine: IconType;
export declare const RiSurroundSoundFill: IconType;
export declare const RiSurroundSoundLine: IconType;
export declare const RiTapeFill: IconType;
export declare const RiTapeLine: IconType;
export declare const RiVideoAddFill: IconType;
export declare const RiVideoAddLine: IconType;
export declare const RiVideoAiFill: IconType;
export declare const RiVideoAiLine: IconType;
export declare const RiVideoDownloadFill: IconType;
export declare const RiVideoDownloadLine: IconType;
export declare const RiVideoFill: IconType;
export declare const RiVideoLine: IconType;
export declare const RiVideoOffFill: IconType;
export declare const RiVideoOffLine: IconType;
export declare const RiVideoOnAiFill: IconType;
export declare const RiVideoOnAiLine: IconType;
export declare const RiVideoOnFill: IconType;
export declare const RiVideoOnLine: IconType;
export declare const RiVideoUploadFill: IconType;
export declare const RiVideoUploadLine: IconType;
export declare const RiVidicon2Fill: IconType;
export declare const RiVidicon2Line: IconType;
export declare const RiVidiconFill: IconType;
export declare const RiVidiconLine: IconType;
export declare const RiVoiceAiFill: IconType;
export declare const RiVoiceAiLine: IconType;
export declare const RiVoiceprintFill: IconType;
export declare const RiVoiceprintLine: IconType;
export declare const RiVolumeDownFill: IconType;
export declare const RiVolumeDownLine: IconType;
export declare const RiVolumeMuteFill: IconType;
export declare const RiVolumeMuteLine: IconType;
export declare const RiVolumeOffVibrateFill: IconType;
export declare const RiVolumeOffVibrateLine: IconType;
export declare const RiVolumeUpFill: IconType;
export declare const RiVolumeUpLine: IconType;
export declare const RiVolumeVibrateFill: IconType;
export declare const RiVolumeVibrateLine: IconType;
export declare const RiWebcamFill: IconType;
export declare const RiWebcamLine: IconType;
export declare const RiArmchairFill: IconType;
export declare const RiArmchairLine: IconType;
export declare const RiBasketballFill: IconType;
export declare const RiBasketballLine: IconType;
export declare const RiBellFill: IconType;
export declare const RiBellLine: IconType;
export declare const RiBilliardsFill: IconType;
export declare const RiBilliardsLine: IconType;
export declare const RiBookShelfFill: IconType;
export declare const RiBookShelfLine: IconType;
export declare const RiBox1Fill: IconType;
export declare const RiBox1Line: IconType;
export declare const RiBox2Fill: IconType;
export declare const RiBox2Line: IconType;
export declare const RiBox3Fill: IconType;
export declare const RiBox3Line: IconType;
export declare const RiBoxingFill: IconType;
export declare const RiBoxingLine: IconType;
export declare const RiCactusFill: IconType;
export declare const RiCactusLine: IconType;
export declare const RiCandleFill: IconType;
export declare const RiCandleLine: IconType;
export declare const RiCharacterRecognitionFill: IconType;
export declare const RiCharacterRecognitionLine: IconType;
export declare const RiCrossFill: IconType;
export declare const RiCrossLine: IconType;
export declare const RiDice1Fill: IconType;
export declare const RiDice1Line: IconType;
export declare const RiDice2Fill: IconType;
export declare const RiDice2Line: IconType;
export declare const RiDice3Fill: IconType;
export declare const RiDice3Line: IconType;
export declare const RiDice4Fill: IconType;
export declare const RiDice4Line: IconType;
export declare const RiDice5Fill: IconType;
export declare const RiDice5Line: IconType;
export declare const RiDice6Fill: IconType;
export declare const RiDice6Line: IconType;
export declare const RiDiceFill: IconType;
export declare const RiDiceLine: IconType;
export declare const RiDoorClosedFill: IconType;
export declare const RiDoorClosedLine: IconType;
export declare const RiDoorFill: IconType;
export declare const RiDoorLine: IconType;
export declare const RiDoorLockBoxFill: IconType;
export declare const RiDoorLockBoxLine: IconType;
export declare const RiDoorLockFill: IconType;
export declare const RiDoorLockLine: IconType;
export declare const RiDoorOpenFill: IconType;
export declare const RiDoorOpenLine: IconType;
export declare const RiFlowerFill: IconType;
export declare const RiFlowerLine: IconType;
export declare const RiFootballFill: IconType;
export declare const RiFootballLine: IconType;
export declare const RiFridgeFill: IconType;
export declare const RiFridgeLine: IconType;
export declare const RiGameFill: IconType;
export declare const RiGameLine: IconType;
export declare const RiGlasses2Fill: IconType;
export declare const RiGlasses2Line: IconType;
export declare const RiGlassesFill: IconType;
export declare const RiGlassesLine: IconType;
export declare const RiGogglesFill: IconType;
export declare const RiGogglesLine: IconType;
export declare const RiGolfBallFill: IconType;
export declare const RiGolfBallLine: IconType;
export declare const RiGraduationCapFill: IconType;
export declare const RiGraduationCapLine: IconType;
export declare const RiHandbagFill: IconType;
export declare const RiHandbagLine: IconType;
export declare const RiInfinityFill: IconType;
export declare const RiInfinityLine: IconType;
export declare const RiKey2Fill: IconType;
export declare const RiKey2Line: IconType;
export declare const RiKeyFill: IconType;
export declare const RiKeyLine: IconType;
export declare const RiLeafFill: IconType;
export declare const RiLeafLine: IconType;
export declare const RiLightbulbFill: IconType;
export declare const RiLightbulbFlashFill: IconType;
export declare const RiLightbulbFlashLine: IconType;
export declare const RiLightbulbLine: IconType;
export declare const RiOutlet2Fill: IconType;
export declare const RiOutlet2Line: IconType;
export declare const RiOutletFill: IconType;
export declare const RiOutletLine: IconType;
export declare const RiPingPongFill: IconType;
export declare const RiPingPongLine: IconType;
export declare const RiPlantFill: IconType;
export declare const RiPlantLine: IconType;
export declare const RiPlug2Fill: IconType;
export declare const RiPlug2Line: IconType;
export declare const RiPlugFill: IconType;
export declare const RiPlugLine: IconType;
export declare const RiPokerClubsFill: IconType;
export declare const RiPokerClubsLine: IconType;
export declare const RiPokerDiamondsFill: IconType;
export declare const RiPokerDiamondsLine: IconType;
export declare const RiPokerHeartsFill: IconType;
export declare const RiPokerHeartsLine: IconType;
export declare const RiPokerSpadesFill: IconType;
export declare const RiPokerSpadesLine: IconType;
export declare const RiPoliceBadgeFill: IconType;
export declare const RiPoliceBadgeLine: IconType;
export declare const RiRecycleFill: IconType;
export declare const RiRecycleLine: IconType;
export declare const RiReservedFill: IconType;
export declare const RiReservedLine: IconType;
export declare const RiScales2Fill: IconType;
export declare const RiScales2Line: IconType;
export declare const RiScales3Fill: IconType;
export declare const RiScales3Line: IconType;
export declare const RiScalesFill: IconType;
export declare const RiScalesLine: IconType;
export declare const RiSeedlingFill: IconType;
export declare const RiSeedlingLine: IconType;
export declare const RiShirtFill: IconType;
export declare const RiShirtLine: IconType;
export declare const RiSofaFill: IconType;
export declare const RiSofaLine: IconType;
export declare const RiStairsFill: IconType;
export declare const RiStairsLine: IconType;
export declare const RiSwordFill: IconType;
export declare const RiSwordLine: IconType;
export declare const RiTShirt2Fill: IconType;
export declare const RiTShirt2Line: IconType;
export declare const RiTShirtAirFill: IconType;
export declare const RiTShirtAirLine: IconType;
export declare const RiTShirtFill: IconType;
export declare const RiTShirtLine: IconType;
export declare const RiToothFill: IconType;
export declare const RiToothLine: IconType;
export declare const RiTreeFill: IconType;
export declare const RiTreeLine: IconType;
export declare const RiUmbrellaFill: IconType;
export declare const RiUmbrellaLine: IconType;
export declare const RiVoiceRecognitionFill: IconType;
export declare const RiVoiceRecognitionLine: IconType;
export declare const RiWeightFill: IconType;
export declare const RiWeightLine: IconType;
export declare const RiWheelchairFill: IconType;
export declare const RiWheelchairLine: IconType;
export declare const RiAddBoxFill: IconType;
export declare const RiAddBoxLine: IconType;
export declare const RiAddCircleFill: IconType;
export declare const RiAddCircleLine: IconType;
export declare const RiAddFill: IconType;
export declare const RiAddLargeFill: IconType;
export declare const RiAddLargeLine: IconType;
export declare const RiAddLine: IconType;
export declare const RiAlarmFill: IconType;
export declare const RiAlarmLine: IconType;
export declare const RiAlarmSnoozeFill: IconType;
export declare const RiAlarmSnoozeLine: IconType;
export declare const RiAlarmWarningFill: IconType;
export declare const RiAlarmWarningLine: IconType;
export declare const RiAlertFill: IconType;
export declare const RiAlertLine: IconType;
export declare const RiApps2AddFill: IconType;
export declare const RiApps2AddLine: IconType;
export declare const RiApps2AiFill: IconType;
export declare const RiApps2AiLine: IconType;
export declare const RiApps2Fill: IconType;
export declare const RiApps2Line: IconType;
export declare const RiAppsFill: IconType;
export declare const RiAppsLine: IconType;
export declare const RiCheckDoubleFill: IconType;
export declare const RiCheckDoubleLine: IconType;
export declare const RiCheckFill: IconType;
export declare const RiCheckLine: IconType;
export declare const RiCheckboxBlankCircleFill: IconType;
export declare const RiCheckboxBlankCircleLine: IconType;
export declare const RiCheckboxBlankFill: IconType;
export declare const RiCheckboxBlankLine: IconType;
export declare const RiCheckboxCircleFill: IconType;
export declare const RiCheckboxCircleLine: IconType;
export declare const RiCheckboxFill: IconType;
export declare const RiCheckboxIndeterminateFill: IconType;
export declare const RiCheckboxIndeterminateLine: IconType;
export declare const RiCheckboxLine: IconType;
export declare const RiCheckboxMultipleBlankFill: IconType;
export declare const RiCheckboxMultipleBlankLine: IconType;
export declare const RiCheckboxMultipleFill: IconType;
export declare const RiCheckboxMultipleLine: IconType;
export declare const RiCloseCircleFill: IconType;
export declare const RiCloseCircleLine: IconType;
export declare const RiCloseFill: IconType;
export declare const RiCloseLargeFill: IconType;
export declare const RiCloseLargeLine: IconType;
export declare const RiCloseLine: IconType;
export declare const RiDashboardFill: IconType;
export declare const RiDashboardHorizontalFill: IconType;
export declare const RiDashboardHorizontalLine: IconType;
export declare const RiDashboardLine: IconType;
export declare const RiDeleteBack2Fill: IconType;
export declare const RiDeleteBack2Line: IconType;
export declare const RiDeleteBackFill: IconType;
export declare const RiDeleteBackLine: IconType;
export declare const RiDeleteBin2Fill: IconType;
export declare const RiDeleteBin2Line: IconType;
export declare const RiDeleteBin3Fill: IconType;
export declare const RiDeleteBin3Line: IconType;
export declare const RiDeleteBin4Fill: IconType;
export declare const RiDeleteBin4Line: IconType;
export declare const RiDeleteBin5Fill: IconType;
export declare const RiDeleteBin5Line: IconType;
export declare const RiDeleteBin6Fill: IconType;
export declare const RiDeleteBin6Line: IconType;
export declare const RiDeleteBin7Fill: IconType;
export declare const RiDeleteBin7Line: IconType;
export declare const RiDeleteBinFill: IconType;
export declare const RiDeleteBinLine: IconType;
export declare const RiDivideFill: IconType;
export declare const RiDivideLine: IconType;
export declare const RiDownload2Fill: IconType;
export declare const RiDownload2Line: IconType;
export declare const RiDownloadCloud2Fill: IconType;
export declare const RiDownloadCloud2Line: IconType;
export declare const RiDownloadCloudFill: IconType;
export declare const RiDownloadCloudLine: IconType;
export declare const RiDownloadFill: IconType;
export declare const RiDownloadLine: IconType;
export declare const RiEqualFill: IconType;
export declare const RiEqualLine: IconType;
export declare const RiErrorWarningFill: IconType;
export declare const RiErrorWarningLine: IconType;
export declare const RiExportFill: IconType;
export declare const RiExportLine: IconType;
export declare const RiExternalLinkFill: IconType;
export declare const RiExternalLinkLine: IconType;
export declare const RiEye2Fill: IconType;
export declare const RiEye2Line: IconType;
export declare const RiEyeCloseFill: IconType;
export declare const RiEyeCloseLine: IconType;
export declare const RiEyeFill: IconType;
export declare const RiEyeLine: IconType;
export declare const RiEyeOffFill: IconType;
export declare const RiEyeOffLine: IconType;
export declare const RiFilter2Fill: IconType;
export declare const RiFilter2Line: IconType;
export declare const RiFilter3Fill: IconType;
export declare const RiFilter3Line: IconType;
export declare const RiFilterFill: IconType;
export declare const RiFilterLine: IconType;
export declare const RiFilterOffFill: IconType;
export declare const RiFilterOffLine: IconType;
export declare const RiFindReplaceFill: IconType;
export declare const RiFindReplaceLine: IconType;
export declare const RiForbid2Fill: IconType;
export declare const RiForbid2Line: IconType;
export declare const RiForbidFill: IconType;
export declare const RiForbidLine: IconType;
export declare const RiFunctionAddFill: IconType;
export declare const RiFunctionAddLine: IconType;
export declare const RiFunctionFill: IconType;
export declare const RiFunctionLine: IconType;
export declare const RiHistoryFill: IconType;
export declare const RiHistoryLine: IconType;
export declare const RiHourglass2Fill: IconType;
export declare const RiHourglass2Line: IconType;
export declare const RiHourglassFill: IconType;
export declare const RiHourglassLine: IconType;
export declare const RiImportFill: IconType;
export declare const RiImportLine: IconType;
export declare const RiIndeterminateCircleFill: IconType;
export declare const RiIndeterminateCircleLine: IconType;
export declare const RiInformation2Fill: IconType;
export declare const RiInformation2Line: IconType;
export declare const RiInformationFill: IconType;
export declare const RiInformationLine: IconType;
export declare const RiInformationOffFill: IconType;
export declare const RiInformationOffLine: IconType;
export declare const RiListSettingsFill: IconType;
export declare const RiListSettingsLine: IconType;
export declare const RiLoader2Fill: IconType;
export declare const RiLoader2Line: IconType;
export declare const RiLoader3Fill: IconType;
export declare const RiLoader3Line: IconType;
export declare const RiLoader4Fill: IconType;
export declare const RiLoader4Line: IconType;
export declare const RiLoader5Fill: IconType;
export declare const RiLoader5Line: IconType;
export declare const RiLoaderFill: IconType;
export declare const RiLoaderLine: IconType;
export declare const RiLock2Fill: IconType;
export declare const RiLock2Line: IconType;
export declare const RiLockFill: IconType;
export declare const RiLockLine: IconType;
export declare const RiLockPasswordFill: IconType;
export declare const RiLockPasswordLine: IconType;
export declare const RiLockStarFill: IconType;
export declare const RiLockStarLine: IconType;
export declare const RiLockUnlockFill: IconType;
export declare const RiLockUnlockLine: IconType;
export declare const RiLoginBoxFill: IconType;
export declare const RiLoginBoxLine: IconType;
export declare const RiLoginCircleFill: IconType;
export declare const RiLoginCircleLine: IconType;
export declare const RiLogoutBoxFill: IconType;
export declare const RiLogoutBoxLine: IconType;
export declare const RiLogoutBoxRFill: IconType;
export declare const RiLogoutBoxRLine: IconType;
export declare const RiLogoutCircleFill: IconType;
export declare const RiLogoutCircleLine: IconType;
export declare const RiLogoutCircleRFill: IconType;
export declare const RiLogoutCircleRLine: IconType;
export declare const RiLoopLeftFill: IconType;
export declare const RiLoopLeftLine: IconType;
export declare const RiLoopRightFill: IconType;
export declare const RiLoopRightLine: IconType;
export declare const RiMenu2Fill: IconType;
export declare const RiMenu2Line: IconType;
export declare const RiMenu3Fill: IconType;
export declare const RiMenu3Line: IconType;
export declare const RiMenu4Fill: IconType;
export declare const RiMenu4Line: IconType;
export declare const RiMenu5Fill: IconType;
export declare const RiMenu5Line: IconType;
export declare const RiMenuAddFill: IconType;
export declare const RiMenuAddLine: IconType;
export declare const RiMenuFill: IconType;
export declare const RiMenuFold2Fill: IconType;
export declare const RiMenuFold2Line: IconType;
export declare const RiMenuFold3Fill: IconType;
export declare const RiMenuFold3Line: IconType;
export declare const RiMenuFold4Fill: IconType;
export declare const RiMenuFold4Line: IconType;
export declare const RiMenuFoldFill: IconType;
export declare const RiMenuFoldLine: IconType;
export declare const RiMenuLine: IconType;
export declare const RiMenuSearchFill: IconType;
export declare const RiMenuSearchLine: IconType;
export declare const RiMenuUnfold2Fill: IconType;
export declare const RiMenuUnfold2Line: IconType;
export declare const RiMenuUnfold3Fill: IconType;
export declare const RiMenuUnfold3Line: IconType;
export declare const RiMenuUnfold4Fill: IconType;
export declare const RiMenuUnfold4Line: IconType;
export declare const RiMenuUnfoldFill: IconType;
export declare const RiMenuUnfoldLine: IconType;
export declare const RiMore2Fill: IconType;
export declare const RiMore2Line: IconType;
export declare const RiMoreFill: IconType;
export declare const RiMoreLine: IconType;
export declare const RiNotificationBadgeFill: IconType;
export declare const RiNotificationBadgeLine: IconType;
export declare const RiProgress1Fill: IconType;
export declare const RiProgress1Line: IconType;
export declare const RiProgress2Fill: IconType;
export declare const RiProgress2Line: IconType;
export declare const RiProgress3Fill: IconType;
export declare const RiProgress3Line: IconType;
export declare const RiProgress4Fill: IconType;
export declare const RiProgress4Line: IconType;
export declare const RiProgress5Fill: IconType;
export declare const RiProgress5Line: IconType;
export declare const RiProgress6Fill: IconType;
export declare const RiProgress6Line: IconType;
export declare const RiProgress7Fill: IconType;
export declare const RiProgress7Line: IconType;
export declare const RiProgress8Fill: IconType;
export declare const RiProgress8Line: IconType;
export declare const RiProhibited2Fill: IconType;
export declare const RiProhibited2Line: IconType;
export declare const RiProhibitedFill: IconType;
export declare const RiProhibitedLine: IconType;
export declare const RiQuestionFill: IconType;
export declare const RiQuestionLine: IconType;
export declare const RiRadioButtonFill: IconType;
export declare const RiRadioButtonLine: IconType;
export declare const RiRefreshFill: IconType;
export declare const RiRefreshLine: IconType;
export declare const RiResetLeftFill: IconType;
export declare const RiResetLeftLine: IconType;
export declare const RiResetRightFill: IconType;
export declare const RiResetRightLine: IconType;
export declare const RiSearch2Fill: IconType;
export declare const RiSearch2Line: IconType;
export declare const RiSearchEyeFill: IconType;
export declare const RiSearchEyeLine: IconType;
export declare const RiSearchFill: IconType;
export declare const RiSearchLine: IconType;
export declare const RiSettings2Fill: IconType;
export declare const RiSettings2Line: IconType;
export declare const RiSettings3Fill: IconType;
export declare const RiSettings3Line: IconType;
export declare const RiSettings4Fill: IconType;
export declare const RiSettings4Line: IconType;
export declare const RiSettings5Fill: IconType;
export declare const RiSettings5Line: IconType;
export declare const RiSettings6Fill: IconType;
export declare const RiSettings6Line: IconType;
export declare const RiSettingsFill: IconType;
export declare const RiSettingsLine: IconType;
export declare const RiShare2Fill: IconType;
export declare const RiShare2Line: IconType;
export declare const RiShareBoxFill: IconType;
export declare const RiShareBoxLine: IconType;
export declare const RiShareCircleFill: IconType;
export declare const RiShareCircleLine: IconType;
export declare const RiShareFill: IconType;
export declare const RiShareForward2Fill: IconType;
export declare const RiShareForward2Line: IconType;
export declare const RiShareForwardBoxFill: IconType;
export declare const RiShareForwardBoxLine: IconType;
export declare const RiShareForwardFill: IconType;
export declare const RiShareForwardLine: IconType;
export declare const RiShareLine: IconType;
export declare const RiShieldCheckFill: IconType;
export declare const RiShieldCheckLine: IconType;
export declare const RiShieldCrossFill: IconType;
export declare const RiShieldCrossLine: IconType;
export declare const RiShieldFill: IconType;
export declare const RiShieldFlashFill: IconType;
export declare const RiShieldFlashLine: IconType;
export declare const RiShieldKeyholeFill: IconType;
export declare const RiShieldKeyholeLine: IconType;
export declare const RiShieldLine: IconType;
export declare const RiShieldStarFill: IconType;
export declare const RiShieldStarLine: IconType;
export declare const RiShieldUserFill: IconType;
export declare const RiShieldUserLine: IconType;
export declare const RiSideBarFill: IconType;
export declare const RiSideBarLine: IconType;
export declare const RiSidebarFoldFill: IconType;
export declare const RiSidebarFoldLine: IconType;
export declare const RiSidebarUnfoldFill: IconType;
export declare const RiSidebarUnfoldLine: IconType;
export declare const RiSpam2Fill: IconType;
export declare const RiSpam2Line: IconType;
export declare const RiSpam3Fill: IconType;
export declare const RiSpam3Line: IconType;
export declare const RiSpamFill: IconType;
export declare const RiSpamLine: IconType;
export declare const RiStarFill: IconType;
export declare const RiStarHalfFill: IconType;
export declare const RiStarHalfLine: IconType;
export declare const RiStarHalfSFill: IconType;
export declare const RiStarHalfSLine: IconType;
export declare const RiStarLine: IconType;
export declare const RiStarOffFill: IconType;
export declare const RiStarOffLine: IconType;
export declare const RiStarSFill: IconType;
export declare const RiStarSLine: IconType;
export declare const RiSubtractFill: IconType;
export declare const RiSubtractLine: IconType;
export declare const RiThumbDownFill: IconType;
export declare const RiThumbDownLine: IconType;
export declare const RiThumbUpFill: IconType;
export declare const RiThumbUpLine: IconType;
export declare const RiTimeFill: IconType;
export declare const RiTimeLine: IconType;
export declare const RiTimer2Fill: IconType;
export declare const RiTimer2Line: IconType;
export declare const RiTimerFill: IconType;
export declare const RiTimerFlashFill: IconType;
export declare const RiTimerFlashLine: IconType;
export declare const RiTimerLine: IconType;
export declare const RiToggleFill: IconType;
export declare const RiToggleLine: IconType;
export declare const RiUpload2Fill: IconType;
export declare const RiUpload2Line: IconType;
export declare const RiUploadCloud2Fill: IconType;
export declare const RiUploadCloud2Line: IconType;
export declare const RiUploadCloudFill: IconType;
export declare const RiUploadCloudLine: IconType;
export declare const RiUploadFill: IconType;
export declare const RiUploadLine: IconType;
export declare const RiZoomInFill: IconType;
export declare const RiZoomInLine: IconType;
export declare const RiZoomOutFill: IconType;
export declare const RiZoomOutLine: IconType;
export declare const RiAccountBox2Fill: IconType;
export declare const RiAccountBox2Line: IconType;
export declare const RiAccountBoxFill: IconType;
export declare const RiAccountBoxLine: IconType;
export declare const RiAccountCircle2Fill: IconType;
export declare const RiAccountCircle2Line: IconType;
export declare const RiAccountCircleFill: IconType;
export declare const RiAccountCircleLine: IconType;
export declare const RiAccountPinBoxFill: IconType;
export declare const RiAccountPinBoxLine: IconType;
export declare const RiAccountPinCircleFill: IconType;
export declare const RiAccountPinCircleLine: IconType;
export declare const RiAdminFill: IconType;
export declare const RiAdminLine: IconType;
export declare const RiAliensFill: IconType;
export declare const RiAliensLine: IconType;
export declare const RiBearSmileFill: IconType;
export declare const RiBearSmileLine: IconType;
export declare const RiBodyScanFill: IconType;
export declare const RiBodyScanLine: IconType;
export declare const RiContactsFill: IconType;
export declare const RiContactsLine: IconType;
export declare const RiCriminalFill: IconType;
export declare const RiCriminalLine: IconType;
export declare const RiEmotion2Fill: IconType;
export declare const RiEmotion2Line: IconType;
export declare const RiEmotionFill: IconType;
export declare const RiEmotionHappyFill: IconType;
export declare const RiEmotionHappyLine: IconType;
export declare const RiEmotionLaughFill: IconType;
export declare const RiEmotionLaughLine: IconType;
export declare const RiEmotionLine: IconType;
export declare const RiEmotionNormalFill: IconType;
export declare const RiEmotionNormalLine: IconType;
export declare const RiEmotionSadFill: IconType;
export declare const RiEmotionSadLine: IconType;
export declare const RiEmotionUnhappyFill: IconType;
export declare const RiEmotionUnhappyLine: IconType;
export declare const RiGenderlessFill: IconType;
export declare const RiGenderlessLine: IconType;
export declare const RiGhost2Fill: IconType;
export declare const RiGhost2Line: IconType;
export declare const RiGhostFill: IconType;
export declare const RiGhostLine: IconType;
export declare const RiGhostSmileFill: IconType;
export declare const RiGhostSmileLine: IconType;
export declare const RiGroup2Fill: IconType;
export declare const RiGroup2Line: IconType;
export declare const RiGroup3Fill: IconType;
export declare const RiGroup3Line: IconType;
export declare const RiGroupFill: IconType;
export declare const RiGroupLine: IconType;
export declare const RiMenFill: IconType;
export declare const RiMenLine: IconType;
export declare const RiMickeyFill: IconType;
export declare const RiMickeyLine: IconType;
export declare const RiOpenArmFill: IconType;
export declare const RiOpenArmLine: IconType;
export declare const RiParentFill: IconType;
export declare const RiParentLine: IconType;
export declare const RiRobot2Fill: IconType;
export declare const RiRobot2Line: IconType;
export declare const RiRobot3Fill: IconType;
export declare const RiRobot3Line: IconType;
export declare const RiRobotFill: IconType;
export declare const RiRobotLine: IconType;
export declare const RiSkull2Fill: IconType;
export declare const RiSkull2Line: IconType;
export declare const RiSkullFill: IconType;
export declare const RiSkullLine: IconType;
export declare const RiSpyFill: IconType;
export declare const RiSpyLine: IconType;
export declare const RiStarSmileFill: IconType;
export declare const RiStarSmileLine: IconType;
export declare const RiTeamFill: IconType;
export declare const RiTeamLine: IconType;
export declare const RiTravestiFill: IconType;
export declare const RiTravestiLine: IconType;
export declare const RiUser2Fill: IconType;
export declare const RiUser2Line: IconType;
export declare const RiUser3Fill: IconType;
export declare const RiUser3Line: IconType;
export declare const RiUser4Fill: IconType;
export declare const RiUser4Line: IconType;
export declare const RiUser5Fill: IconType;
export declare const RiUser5Line: IconType;
export declare const RiUser6Fill: IconType;
export declare const RiUser6Line: IconType;
export declare const RiUserAddFill: IconType;
export declare const RiUserAddLine: IconType;
export declare const RiUserCommunityFill: IconType;
export declare const RiUserCommunityLine: IconType;
export declare const RiUserFill: IconType;
export declare const RiUserFollowFill: IconType;
export declare const RiUserFollowLine: IconType;
export declare const RiUserForbidFill: IconType;
export declare const RiUserForbidLine: IconType;
export declare const RiUserHeartFill: IconType;
export declare const RiUserHeartLine: IconType;
export declare const RiUserLine: IconType;
export declare const RiUserLocationFill: IconType;
export declare const RiUserLocationLine: IconType;
export declare const RiUserMinusFill: IconType;
export declare const RiUserMinusLine: IconType;
export declare const RiUserReceived2Fill: IconType;
export declare const RiUserReceived2Line: IconType;
export declare const RiUserReceivedFill: IconType;
export declare const RiUserReceivedLine: IconType;
export declare const RiUserSearchFill: IconType;
export declare const RiUserSearchLine: IconType;
export declare const RiUserSettingsFill: IconType;
export declare const RiUserSettingsLine: IconType;
export declare const RiUserShared2Fill: IconType;
export declare const RiUserShared2Line: IconType;
export declare const RiUserSharedFill: IconType;
export declare const RiUserSharedLine: IconType;
export declare const RiUserSmileFill: IconType;
export declare const RiUserSmileLine: IconType;
export declare const RiUserStarFill: IconType;
export declare const RiUserStarLine: IconType;
export declare const RiUserUnfollowFill: IconType;
export declare const RiUserUnfollowLine: IconType;
export declare const RiUserVoiceFill: IconType;
export declare const RiUserVoiceLine: IconType;
export declare const RiWomenFill: IconType;
export declare const RiWomenLine: IconType;
export declare const RiBlazeFill: IconType;
export declare const RiBlazeLine: IconType;
export declare const RiCelsiusFill: IconType;
export declare const RiCelsiusLine: IconType;
export declare const RiCloudWindyFill: IconType;
export declare const RiCloudWindyLine: IconType;
export declare const RiCloudy2Fill: IconType;
export declare const RiCloudy2Line: IconType;
export declare const RiCloudyFill: IconType;
export declare const RiCloudyLine: IconType;
export declare const RiDrizzleFill: IconType;
export declare const RiDrizzleLine: IconType;
export declare const RiEarthquakeFill: IconType;
export declare const RiEarthquakeLine: IconType;
export declare const RiFahrenheitFill: IconType;
export declare const RiFahrenheitLine: IconType;
export declare const RiFireFill: IconType;
export declare const RiFireLine: IconType;
export declare const RiFlashlightFill: IconType;
export declare const RiFlashlightLine: IconType;
export declare const RiFloodFill: IconType;
export declare const RiFloodLine: IconType;
export declare const RiFoggyFill: IconType;
export declare const RiFoggyLine: IconType;
export declare const RiHailFill: IconType;
export declare const RiHailLine: IconType;
export declare const RiHaze2Fill: IconType;
export declare const RiHaze2Line: IconType;
export declare const RiHazeFill: IconType;
export declare const RiHazeLine: IconType;
export declare const RiHeavyShowersFill: IconType;
export declare const RiHeavyShowersLine: IconType;
export declare const RiMeteorFill: IconType;
export declare const RiMeteorLine: IconType;
export declare const RiMistFill: IconType;
export declare const RiMistLine: IconType;
export declare const RiMoonClearFill: IconType;
export declare const RiMoonClearLine: IconType;
export declare const RiMoonCloudyFill: IconType;
export declare const RiMoonCloudyLine: IconType;
export declare const RiMoonFill: IconType;
export declare const RiMoonFoggyFill: IconType;
export declare const RiMoonFoggyLine: IconType;
export declare const RiMoonLine: IconType;
export declare const RiRainbowFill: IconType;
export declare const RiRainbowLine: IconType;
export declare const RiRainyFill: IconType;
export declare const RiRainyLine: IconType;
export declare const RiShining2Fill: IconType;
export declare const RiShining2Line: IconType;
export declare const RiShiningFill: IconType;
export declare const RiShiningLine: IconType;
export declare const RiShowersFill: IconType;
export declare const RiShowersLine: IconType;
export declare const RiSnowflakeFill: IconType;
export declare const RiSnowflakeLine: IconType;
export declare const RiSnowyFill: IconType;
export declare const RiSnowyLine: IconType;
export declare const RiSparkling2Fill: IconType;
export declare const RiSparkling2Line: IconType;
export declare const RiSparklingFill: IconType;
export declare const RiSparklingLine: IconType;
export declare const RiSunCloudyFill: IconType;
export declare const RiSunCloudyLine: IconType;
export declare const RiSunFill: IconType;
export declare const RiSunFoggyFill: IconType;
export declare const RiSunFoggyLine: IconType;
export declare const RiSunLine: IconType;
export declare const RiTempColdFill: IconType;
export declare const RiTempColdLine: IconType;
export declare const RiTempHotFill: IconType;
export declare const RiTempHotLine: IconType;
export declare const RiThunderstormsFill: IconType;
export declare const RiThunderstormsLine: IconType;
export declare const RiTornadoFill: IconType;
export declare const RiTornadoLine: IconType;
export declare const RiTyphoonFill: IconType;
export declare const RiTyphoonLine: IconType;
export declare const RiWaterPercentFill: IconType;
export declare const RiWaterPercentLine: IconType;
export declare const RiWindyFill: IconType;
export declare const RiWindyLine: IconType;
