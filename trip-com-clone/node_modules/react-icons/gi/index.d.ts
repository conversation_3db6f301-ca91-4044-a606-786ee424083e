// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const Gi3dGlasses: IconType;
export declare const Gi3dHammer: IconType;
export declare const Gi3dMeeple: IconType;
export declare const Gi3dStairs: IconType;
export declare const GiAbacus: IconType;
export declare const GiAbbotMeeple: IconType;
export declare const GiAbdominalArmor: IconType;
export declare const GiAbstract001: IconType;
export declare const GiAbstract002: IconType;
export declare const GiAbstract003: IconType;
export declare const GiAbstract004: IconType;
export declare const GiAbstract005: IconType;
export declare const GiAbstract006: IconType;
export declare const GiAbstract007: IconType;
export declare const GiAbstract008: IconType;
export declare const GiAbstract009: IconType;
export declare const GiAbstract010: IconType;
export declare const GiAbstract011: IconType;
export declare const GiAbstract012: IconType;
export declare const GiAbstract013: IconType;
export declare const GiAbstract014: IconType;
export declare const GiAbstract015: IconType;
export declare const GiAbstract016: IconType;
export declare const GiAbstract017: IconType;
export declare const GiAbstract018: IconType;
export declare const GiAbstract019: IconType;
export declare const GiAbstract020: IconType;
export declare const GiAbstract021: IconType;
export declare const GiAbstract022: IconType;
export declare const GiAbstract023: IconType;
export declare const GiAbstract024: IconType;
export declare const GiAbstract025: IconType;
export declare const GiAbstract026: IconType;
export declare const GiAbstract027: IconType;
export declare const GiAbstract028: IconType;
export declare const GiAbstract029: IconType;
export declare const GiAbstract030: IconType;
export declare const GiAbstract031: IconType;
export declare const GiAbstract032: IconType;
export declare const GiAbstract033: IconType;
export declare const GiAbstract034: IconType;
export declare const GiAbstract035: IconType;
export declare const GiAbstract036: IconType;
export declare const GiAbstract037: IconType;
export declare const GiAbstract038: IconType;
export declare const GiAbstract039: IconType;
export declare const GiAbstract040: IconType;
export declare const GiAbstract041: IconType;
export declare const GiAbstract042: IconType;
export declare const GiAbstract043: IconType;
export declare const GiAbstract044: IconType;
export declare const GiAbstract045: IconType;
export declare const GiAbstract046: IconType;
export declare const GiAbstract047: IconType;
export declare const GiAbstract048: IconType;
export declare const GiAbstract049: IconType;
export declare const GiAbstract050: IconType;
export declare const GiAbstract051: IconType;
export declare const GiAbstract052: IconType;
export declare const GiAbstract053: IconType;
export declare const GiAbstract054: IconType;
export declare const GiAbstract055: IconType;
export declare const GiAbstract056: IconType;
export declare const GiAbstract057: IconType;
export declare const GiAbstract058: IconType;
export declare const GiAbstract059: IconType;
export declare const GiAbstract060: IconType;
export declare const GiAbstract061: IconType;
export declare const GiAbstract062: IconType;
export declare const GiAbstract063: IconType;
export declare const GiAbstract064: IconType;
export declare const GiAbstract065: IconType;
export declare const GiAbstract066: IconType;
export declare const GiAbstract067: IconType;
export declare const GiAbstract068: IconType;
export declare const GiAbstract069: IconType;
export declare const GiAbstract070: IconType;
export declare const GiAbstract071: IconType;
export declare const GiAbstract072: IconType;
export declare const GiAbstract073: IconType;
export declare const GiAbstract074: IconType;
export declare const GiAbstract075: IconType;
export declare const GiAbstract076: IconType;
export declare const GiAbstract077: IconType;
export declare const GiAbstract078: IconType;
export declare const GiAbstract079: IconType;
export declare const GiAbstract080: IconType;
export declare const GiAbstract081: IconType;
export declare const GiAbstract082: IconType;
export declare const GiAbstract083: IconType;
export declare const GiAbstract084: IconType;
export declare const GiAbstract085: IconType;
export declare const GiAbstract086: IconType;
export declare const GiAbstract087: IconType;
export declare const GiAbstract088: IconType;
export declare const GiAbstract089: IconType;
export declare const GiAbstract090: IconType;
export declare const GiAbstract091: IconType;
export declare const GiAbstract092: IconType;
export declare const GiAbstract093: IconType;
export declare const GiAbstract094: IconType;
export declare const GiAbstract095: IconType;
export declare const GiAbstract096: IconType;
export declare const GiAbstract097: IconType;
export declare const GiAbstract098: IconType;
export declare const GiAbstract099: IconType;
export declare const GiAbstract100: IconType;
export declare const GiAbstract101: IconType;
export declare const GiAbstract102: IconType;
export declare const GiAbstract103: IconType;
export declare const GiAbstract104: IconType;
export declare const GiAbstract105: IconType;
export declare const GiAbstract106: IconType;
export declare const GiAbstract107: IconType;
export declare const GiAbstract108: IconType;
export declare const GiAbstract109: IconType;
export declare const GiAbstract110: IconType;
export declare const GiAbstract111: IconType;
export declare const GiAbstract112: IconType;
export declare const GiAbstract113: IconType;
export declare const GiAbstract114: IconType;
export declare const GiAbstract115: IconType;
export declare const GiAbstract116: IconType;
export declare const GiAbstract117: IconType;
export declare const GiAbstract118: IconType;
export declare const GiAbstract119: IconType;
export declare const GiAbstract120: IconType;
export declare const GiAbstract121: IconType;
export declare const GiAccordion: IconType;
export declare const GiAce: IconType;
export declare const GiAchievement: IconType;
export declare const GiAchillesHeel: IconType;
export declare const GiAcidBlob: IconType;
export declare const GiAcidTube: IconType;
export declare const GiAcid: IconType;
export declare const GiAcorn: IconType;
export declare const GiAcousticMegaphone: IconType;
export declare const GiAcrobatic: IconType;
export declare const GiAerialSignal: IconType;
export declare const GiAerodynamicHarpoon: IconType;
export declare const GiAerosol: IconType;
export declare const GiAfrica: IconType;
export declare const GiAfterburn: IconType;
export declare const GiAgave: IconType;
export declare const GiAges: IconType;
export declare const GiAirBalloon: IconType;
export declare const GiAirForce: IconType;
export declare const GiAirMan: IconType;
export declare const GiAirZigzag: IconType;
export declare const GiAirplaneArrival: IconType;
export declare const GiAirplaneDeparture: IconType;
export declare const GiAirplane: IconType;
export declare const GiAirtightHatch: IconType;
export declare const GiAk47: IconType;
export declare const GiAk47U: IconType;
export declare const GiAkhet: IconType;
export declare const GiAlarmClock: IconType;
export declare const GiAlgae: IconType;
export declare const GiAlgeria: IconType;
export declare const GiAlienBug: IconType;
export declare const GiAlienEgg: IconType;
export declare const GiAlienFire: IconType;
export declare const GiAlienSkull: IconType;
export declare const GiAlienStare: IconType;
export declare const GiAllForOne: IconType;
export declare const GiAllSeeingEye: IconType;
export declare const GiAlliedStar: IconType;
export declare const GiAlligatorClip: IconType;
export declare const GiAlmond: IconType;
export declare const GiAmberMosquito: IconType;
export declare const GiAmbulance: IconType;
export declare const GiAmericanFootballBall: IconType;
export declare const GiAmericanFootballHelmet: IconType;
export declare const GiAmericanFootballPlayer: IconType;
export declare const GiAmericanShield: IconType;
export declare const GiAmethyst: IconType;
export declare const GiAmmoBox: IconType;
export declare const GiAmmoniteFossil: IconType;
export declare const GiAmmonite: IconType;
export declare const GiAmphora: IconType;
export declare const GiAmpleDress: IconType;
export declare const GiAmplitude: IconType;
export declare const GiAmputation: IconType;
export declare const GiAnarchy: IconType;
export declare const GiAnatomy: IconType;
export declare const GiAnchor: IconType;
export declare const GiAncientColumns: IconType;
export declare const GiAncientRuins: IconType;
export declare const GiAncientScrew: IconType;
export declare const GiAncientSword: IconType;
export declare const GiAndroidMask: IconType;
export declare const GiAndromedaChain: IconType;
export declare const GiAngelOutfit: IconType;
export declare const GiAngelWings: IconType;
export declare const GiAnglerFish: IconType;
export declare const GiAngola: IconType;
export declare const GiAngryEyes: IconType;
export declare const GiAngularSpider: IconType;
export declare const GiAnimalHide: IconType;
export declare const GiAnimalSkull: IconType;
export declare const GiAnkh: IconType;
export declare const GiAnnexation: IconType;
export declare const GiAnt: IconType;
export declare const GiAntarctica: IconType;
export declare const GiAnteater: IconType;
export declare const GiAnthem: IconType;
export declare const GiAntiAircraftGun: IconType;
export declare const GiAntibody: IconType;
export declare const GiAnticlockwiseRotation: IconType;
export declare const GiAnts: IconType;
export declare const GiAnubis: IconType;
export declare const GiAnvilImpact: IconType;
export declare const GiAnvil: IconType;
export declare const GiApc: IconType;
export declare const GiApolloCapsule: IconType;
export declare const GiApothecary: IconType;
export declare const GiAppleCore: IconType;
export declare const GiAppleMaggot: IconType;
export declare const GiAppleSeeds: IconType;
export declare const GiAquarium: IconType;
export declare const GiAquarius: IconType;
export declare const GiAqueduct: IconType;
export declare const GiArabicDoor: IconType;
export declare const GiArcTriomphe: IconType;
export declare const GiArchBridge: IconType;
export declare const GiArchaeopteryxFossil: IconType;
export declare const GiArcher: IconType;
export declare const GiArcheryTarget: IconType;
export declare const GiArchitectMask: IconType;
export declare const GiArchiveRegister: IconType;
export declare const GiArchiveResearch: IconType;
export declare const GiArcingBolt: IconType;
export declare const GiArena: IconType;
export declare const GiAries: IconType;
export declare const GiArmBandage: IconType;
export declare const GiArmSling: IconType;
export declare const GiArm: IconType;
export declare const GiArmadilloTail: IconType;
export declare const GiArmadillo: IconType;
export declare const GiArmorDowngrade: IconType;
export declare const GiArmorPunch: IconType;
export declare const GiArmorUpgrade: IconType;
export declare const GiArmorVest: IconType;
export declare const GiArmoredBoomerang: IconType;
export declare const GiArmoredPants: IconType;
export declare const GiArmouredShell: IconType;
export declare const GiArrest: IconType;
export declare const GiArrowCluster: IconType;
export declare const GiArrowCursor: IconType;
export declare const GiArrowDunk: IconType;
export declare const GiArrowFlights: IconType;
export declare const GiArrowScope: IconType;
export declare const GiArrowWings: IconType;
export declare const GiArrowed: IconType;
export declare const GiArrowhead: IconType;
export declare const GiArrowsShield: IconType;
export declare const GiArson: IconType;
export declare const GiArtificialHive: IconType;
export declare const GiArtificialIntelligence: IconType;
export declare const GiArtilleryShell: IconType;
export declare const GiAscendingBlock: IconType;
export declare const GiAsianLantern: IconType;
export declare const GiAsparagus: IconType;
export declare const GiAspergillum: IconType;
export declare const GiAssassinPocket: IconType;
export declare const GiAsteroid: IconType;
export declare const GiAstrolabe: IconType;
export declare const GiAstronautHelmet: IconType;
export declare const GiAtSea: IconType;
export declare const GiAtlas: IconType;
export declare const GiAtomCore: IconType;
export declare const GiAtom: IconType;
export declare const GiAtomicSlashes: IconType;
export declare const GiAttachedShield: IconType;
export declare const GiAubergine: IconType;
export declare const GiAudioCassette: IconType;
export declare const GiAura: IconType;
export declare const GiAustralia: IconType;
export declare const GiAutoRepair: IconType;
export declare const GiAutogun: IconType;
export declare const GiAutomaticSas: IconType;
export declare const GiAvocado: IconType;
export declare const GiAvoidance: IconType;
export declare const GiAwareness: IconType;
export declare const GiAxeInLog: IconType;
export declare const GiAxeInStump: IconType;
export declare const GiAxeSwing: IconType;
export declare const GiAxeSword: IconType;
export declare const GiAxolotl: IconType;
export declare const GiAztecCalendarSun: IconType;
export declare const GiAzulFlake: IconType;
export declare const GiBabyBottle: IconType;
export declare const GiBabyFace: IconType;
export declare const GiBabyfootPlayers: IconType;
export declare const GiBackForth: IconType;
export declare const GiBackPain: IconType;
export declare const GiBackboneShell: IconType;
export declare const GiBackgammon: IconType;
export declare const GiBackpack: IconType;
export declare const GiBackstab: IconType;
export declare const GiBackup: IconType;
export declare const GiBackwardTime: IconType;
export declare const GiBacon: IconType;
export declare const GiBadBreath: IconType;
export declare const GiBadGnome: IconType;
export declare const GiBagpipes: IconType;
export declare const GiBalaclava: IconType;
export declare const GiBalkenkreuz: IconType;
export declare const GiBallGlow: IconType;
export declare const GiBallHeart: IconType;
export declare const GiBallPyramid: IconType;
export declare const GiBallerinaShoes: IconType;
export declare const GiBallista: IconType;
export declare const GiBalloonDog: IconType;
export declare const GiBalloons: IconType;
export declare const GiBambooFountain: IconType;
export declare const GiBamboo: IconType;
export declare const GiBananaBunch: IconType;
export declare const GiBananaPeel: IconType;
export declare const GiBananaPeeled: IconType;
export declare const GiBanana: IconType;
export declare const GiBandageRoll: IconType;
export declare const GiBandaged: IconType;
export declare const GiBandana: IconType;
export declare const GiBandit: IconType;
export declare const GiBangingGavel: IconType;
export declare const GiBanjo: IconType;
export declare const GiBank: IconType;
export declare const GiBanknote: IconType;
export declare const GiBaobab: IconType;
export declare const GiBarStool: IconType;
export declare const GiBarbarian: IconType;
export declare const GiBarbecue: IconType;
export declare const GiBarbedArrow: IconType;
export declare const GiBarbedCoil: IconType;
export declare const GiBarbedNails: IconType;
export declare const GiBarbedSpear: IconType;
export declare const GiBarbedStar: IconType;
export declare const GiBarbedSun: IconType;
export declare const GiBarbedWire: IconType;
export declare const GiBarbute: IconType;
export declare const GiBarefoot: IconType;
export declare const GiBarn: IconType;
export declare const GiBarracksTent: IconType;
export declare const GiBarracks: IconType;
export declare const GiBarrelLeak: IconType;
export declare const GiBarrel: IconType;
export declare const GiBarricade: IconType;
export declare const GiBarrier: IconType;
export declare const GiBaseDome: IconType;
export declare const GiBaseballBat: IconType;
export declare const GiBaseballGlove: IconType;
export declare const GiBasket: IconType;
export declare const GiBasketballBall: IconType;
export declare const GiBasketballBasket: IconType;
export declare const GiBasketballJersey: IconType;
export declare const GiBassetHoundHead: IconType;
export declare const GiBassoon: IconType;
export declare const GiBastet: IconType;
export declare const GiBatBlade: IconType;
export declare const GiBatLeth: IconType;
export declare const GiBatMask: IconType;
export declare const GiBatWing: IconType;
export declare const GiBat: IconType;
export declare const GiBathtub: IconType;
export declare const GiBaton: IconType;
export declare const GiBatteredAxe: IconType;
export declare const GiBatteries: IconType;
export declare const GiBattery0: IconType;
export declare const GiBattery100: IconType;
export declare const GiBattery25: IconType;
export declare const GiBattery50: IconType;
export declare const GiBattery75: IconType;
export declare const GiBatteryMinus: IconType;
export declare const GiBatteryPackAlt: IconType;
export declare const GiBatteryPack: IconType;
export declare const GiBatteryPlus: IconType;
export declare const GiBattleAxe: IconType;
export declare const GiBattleGear: IconType;
export declare const GiBattleMech: IconType;
export declare const GiBattleTank: IconType;
export declare const GiBattleship: IconType;
export declare const GiBatwingEmblem: IconType;
export declare const GiBayonet: IconType;
export declare const GiBeachBag: IconType;
export declare const GiBeachBall: IconType;
export declare const GiBeachBucket: IconType;
export declare const GiBeamSatellite: IconType;
export declare const GiBeamWake: IconType;
export declare const GiBeamsAura: IconType;
export declare const GiBeanstalk: IconType;
export declare const GiBearFace: IconType;
export declare const GiBearHead: IconType;
export declare const GiBeard: IconType;
export declare const GiBeastEye: IconType;
export declare const GiBeaver: IconType;
export declare const GiBedLamp: IconType;
export declare const GiBed: IconType;
export declare const GiBee: IconType;
export declare const GiBeech: IconType;
export declare const GiBeehive: IconType;
export declare const GiBeerBottle: IconType;
export declare const GiBeerHorn: IconType;
export declare const GiBeerStein: IconType;
export declare const GiBeet: IconType;
export declare const GiBeetleShell: IconType;
export declare const GiBehold: IconType;
export declare const GiBelgium: IconType;
export declare const GiBellPepper: IconType;
export declare const GiBellShield: IconType;
export declare const GiBellows: IconType;
export declare const GiBeltArmor: IconType;
export declare const GiBeltBuckles: IconType;
export declare const GiBelt: IconType;
export declare const GiBerriesBowl: IconType;
export declare const GiBerryBush: IconType;
export declare const GiBestialFangs: IconType;
export declare const GiBeveledStar: IconType;
export declare const GiBiceps: IconType;
export declare const GiBigDiamondRing: IconType;
export declare const GiBigEgg: IconType;
export declare const GiBigGear: IconType;
export declare const GiBigWave: IconType;
export declare const GiBilledCap: IconType;
export declare const GiBindle: IconType;
export declare const GiBinoculars: IconType;
export declare const GiBiohazard: IconType;
export declare const GiBiplane: IconType;
export declare const GiBirchTrees: IconType;
export declare const GiBirdCage: IconType;
export declare const GiBirdClaw: IconType;
export declare const GiBirdHouse: IconType;
export declare const GiBirdLimb: IconType;
export declare const GiBirdMask: IconType;
export declare const GiBirdTwitter: IconType;
export declare const GiBison: IconType;
export declare const GiBlackBar: IconType;
export declare const GiBlackBelt: IconType;
export declare const GiBlackBook: IconType;
export declare const GiBlackBridge: IconType;
export declare const GiBlackFlag: IconType;
export declare const GiBlackHandShield: IconType;
export declare const GiBlackHoleBolas: IconType;
export declare const GiBlackKnightHelm: IconType;
export declare const GiBlackSea: IconType;
export declare const GiBlackball: IconType;
export declare const GiBlackcurrant: IconType;
export declare const GiBlacksmith: IconType;
export declare const GiBladeBite: IconType;
export declare const GiBladeDrag: IconType;
export declare const GiBladeFall: IconType;
export declare const GiBlast: IconType;
export declare const GiBlaster: IconType;
export declare const GiBleedingEye: IconType;
export declare const GiBleedingHeart: IconType;
export declare const GiBleedingWound: IconType;
export declare const GiBlender: IconType;
export declare const GiBlindfold: IconType;
export declare const GiBlockHouse: IconType;
export declare const GiBlood: IconType;
export declare const GiBloodyStash: IconType;
export declare const GiBloodySword: IconType;
export declare const GiBlunderbuss: IconType;
export declare const GiBo: IconType;
export declare const GiBoarEnsign: IconType;
export declare const GiBoarTusks: IconType;
export declare const GiBoar: IconType;
export declare const GiBoatEngine: IconType;
export declare const GiBoatFishing: IconType;
export declare const GiBoatHorizon: IconType;
export declare const GiBoatPropeller: IconType;
export declare const GiBoba: IconType;
export declare const GiBodyBalance: IconType;
export declare const GiBodyHeight: IconType;
export declare const GiBodySwapping: IconType;
export declare const GiBoilingBubbles: IconType;
export declare const GiBokChoy: IconType;
export declare const GiBolas: IconType;
export declare const GiBolivia: IconType;
export declare const GiBoltBomb: IconType;
export declare const GiBoltCutter: IconType;
export declare const GiBoltDrop: IconType;
export declare const GiBoltEye: IconType;
export declare const GiBoltSaw: IconType;
export declare const GiBoltShield: IconType;
export declare const GiBoltSpellCast: IconType;
export declare const GiBolterGun: IconType;
export declare const GiBomber: IconType;
export declare const GiBombingRun: IconType;
export declare const GiBoneGnawer: IconType;
export declare const GiBoneKnife: IconType;
export declare const GiBoneMace: IconType;
export declare const GiBonsaiTree: IconType;
export declare const GiBookAura: IconType;
export declare const GiBookCover: IconType;
export declare const GiBookPile: IconType;
export declare const GiBookStorm: IconType;
export declare const GiBookmark: IconType;
export declare const GiBookmarklet: IconType;
export declare const GiBookshelf: IconType;
export declare const GiBoombox: IconType;
export declare const GiBoomerangCross: IconType;
export declare const GiBoomerangSun: IconType;
export declare const GiBoomerang: IconType;
export declare const GiBootKick: IconType;
export declare const GiBootPrints: IconType;
export declare const GiBootStomp: IconType;
export declare const GiBoots: IconType;
export declare const GiBooze: IconType;
export declare const GiBorderedShield: IconType;
export declare const GiBossKey: IconType;
export declare const GiBottleCap: IconType;
export declare const GiBottleVapors: IconType;
export declare const GiBottledBolt: IconType;
export declare const GiBottledShadow: IconType;
export declare const GiBottomRight3dArrow: IconType;
export declare const GiBoulderDash: IconType;
export declare const GiBouncingSpring: IconType;
export declare const GiBouncingSword: IconType;
export declare const GiBowArrow: IconType;
export declare const GiBowString: IconType;
export declare const GiBowTieRibbon: IconType;
export declare const GiBowTie: IconType;
export declare const GiBowels: IconType;
export declare const GiBowenKnot: IconType;
export declare const GiBowieKnife: IconType;
export declare const GiBowlOfRice: IconType;
export declare const GiBowlSpiral: IconType;
export declare const GiBowlingAlley: IconType;
export declare const GiBowlingPin: IconType;
export declare const GiBowlingPropulsion: IconType;
export declare const GiBowlingStrike: IconType;
export declare const GiBowman: IconType;
export declare const GiBoxCutter: IconType;
export declare const GiBoxTrap: IconType;
export declare const GiBoxUnpacking: IconType;
export declare const GiBoxingGloveSurprise: IconType;
export declare const GiBoxingGlove: IconType;
export declare const GiBoxingRing: IconType;
export declare const GiBracer: IconType;
export declare const GiBracers: IconType;
export declare const GiBrainDump: IconType;
export declare const GiBrainFreeze: IconType;
export declare const GiBrainLeak: IconType;
export declare const GiBrainStem: IconType;
export declare const GiBrainTentacle: IconType;
export declare const GiBrain: IconType;
export declare const GiBrainstorm: IconType;
export declare const GiBranchArrow: IconType;
export declare const GiBrandyBottle: IconType;
export declare const GiBrasero: IconType;
export declare const GiBrassEye: IconType;
export declare const GiBrassKnuckles: IconType;
export declare const GiBrazilFlag: IconType;
export declare const GiBrazil: IconType;
export declare const GiBreadSlice: IconType;
export declare const GiBread: IconType;
export declare const GiBreakingChain: IconType;
export declare const GiBreastplate: IconType;
export declare const GiBrickPile: IconType;
export declare const GiBrickWall: IconType;
export declare const GiBridge: IconType;
export declare const GiBriefcase: IconType;
export declare const GiBrightExplosion: IconType;
export declare const GiBroadDagger: IconType;
export declare const GiBroadheadArrow: IconType;
export declare const GiBroadsword: IconType;
export declare const GiBroccoli: IconType;
export declare const GiBrodieHelmet: IconType;
export declare const GiBrokenArrow: IconType;
export declare const GiBrokenAxe: IconType;
export declare const GiBrokenBone: IconType;
export declare const GiBrokenBottle: IconType;
export declare const GiBrokenHeartZone: IconType;
export declare const GiBrokenHeart: IconType;
export declare const GiBrokenPottery: IconType;
export declare const GiBrokenRibbon: IconType;
export declare const GiBrokenShield: IconType;
export declare const GiBrokenSkull: IconType;
export declare const GiBrokenTablet: IconType;
export declare const GiBrokenWall: IconType;
export declare const GiBroom: IconType;
export declare const GiBrutalHelm: IconType;
export declare const GiBrute: IconType;
export declare const GiBubbleField: IconType;
export declare const GiBubbles: IconType;
export declare const GiBubblingBeam: IconType;
export declare const GiBubblingBowl: IconType;
export declare const GiBubblingFlask: IconType;
export declare const GiBud: IconType;
export declare const GiBuffaloHead: IconType;
export declare const GiBugNet: IconType;
export declare const GiBugleCall: IconType;
export declare const GiBulb: IconType;
export declare const GiBulgaria: IconType;
export declare const GiBullHorns: IconType;
export declare const GiBull: IconType;
export declare const GiBulldozer: IconType;
export declare const GiBulletBill: IconType;
export declare const GiBulletImpacts: IconType;
export declare const GiBullets: IconType;
export declare const GiBullseye: IconType;
export declare const GiBullyMinion: IconType;
export declare const GiBundleGrenade: IconType;
export declare const GiBunkBeds: IconType;
export declare const GiBunkerAssault: IconType;
export declare const GiBunker: IconType;
export declare const GiBunnySlippers: IconType;
export declare const GiBuoy: IconType;
export declare const GiBurn: IconType;
export declare const GiBurningBlobs: IconType;
export declare const GiBurningBook: IconType;
export declare const GiBurningDot: IconType;
export declare const GiBurningEmbers: IconType;
export declare const GiBurningEye: IconType;
export declare const GiBurningForest: IconType;
export declare const GiBurningMeteor: IconType;
export declare const GiBurningPassion: IconType;
export declare const GiBurningRoundShot: IconType;
export declare const GiBurningSkull: IconType;
export declare const GiBurningTree: IconType;
export declare const GiBurstBlob: IconType;
export declare const GiBusDoors: IconType;
export declare const GiBusStop: IconType;
export declare const GiBus: IconType;
export declare const GiButterToast: IconType;
export declare const GiButter: IconType;
export declare const GiButterflyFlower: IconType;
export declare const GiButterflyKnife: IconType;
export declare const GiButterflyWarning: IconType;
export declare const GiButterfly: IconType;
export declare const GiButtonFinger: IconType;
export declare const GiBuyCard: IconType;
export declare const GiByzantinTemple: IconType;
export declare const GiC96: IconType;
export declare const GiCabbage: IconType;
export declare const GiCableStayedBridge: IconType;
export declare const GiCactusPot: IconType;
export declare const GiCactusTap: IconType;
export declare const GiCactus: IconType;
export declare const GiCadillacHelm: IconType;
export declare const GiCaduceus: IconType;
export declare const GiCaesar: IconType;
export declare const GiCage: IconType;
export declare const GiCagedBall: IconType;
export declare const GiCakeSlice: IconType;
export declare const GiCalavera: IconType;
export declare const GiCalculator: IconType;
export declare const GiCaldera: IconType;
export declare const GiCalendarHalfYear: IconType;
export declare const GiCalendar: IconType;
export declare const GiCaltrops: IconType;
export declare const GiCamargueCross: IconType;
export declare const GiCambodia: IconType;
export declare const GiCamelHead: IconType;
export declare const GiCamel: IconType;
export declare const GiCampCookingPot: IconType;
export declare const GiCampfire: IconType;
export declare const GiCampingTent: IconType;
export declare const GiCancel: IconType;
export declare const GiCancer: IconType;
export declare const GiCandleFlame: IconType;
export declare const GiCandleHolder: IconType;
export declare const GiCandleLight: IconType;
export declare const GiCandleSkull: IconType;
export declare const GiCandlebright: IconType;
export declare const GiCandles: IconType;
export declare const GiCandlestickPhone: IconType;
export declare const GiCandyCanes: IconType;
export declare const GiCannedFish: IconType;
export declare const GiCannister: IconType;
export declare const GiCannonBall: IconType;
export declare const GiCannonShot: IconType;
export declare const GiCannon: IconType;
export declare const GiCanoe: IconType;
export declare const GiCantua: IconType;
export declare const GiCapeArmor: IconType;
export declare const GiCape: IconType;
export declare const GiCapitol: IconType;
export declare const GiCapricorn: IconType;
export declare const GiCaptainHatProfile: IconType;
export declare const GiCapybara: IconType;
export declare const GiCarBattery: IconType;
export declare const GiCarDoor: IconType;
export declare const GiCarKey: IconType;
export declare const GiCarSeat: IconType;
export declare const GiCarWheel: IconType;
export declare const GiCarabiner: IconType;
export declare const GiCarambola: IconType;
export declare const GiCaravan: IconType;
export declare const GiCaravel: IconType;
export declare const GiCard10Clubs: IconType;
export declare const GiCard10Diamonds: IconType;
export declare const GiCard10Hearts: IconType;
export declare const GiCard10Spades: IconType;
export declare const GiCard2Clubs: IconType;
export declare const GiCard2Diamonds: IconType;
export declare const GiCard2Hearts: IconType;
export declare const GiCard2Spades: IconType;
export declare const GiCard3Clubs: IconType;
export declare const GiCard3Diamonds: IconType;
export declare const GiCard3Hearts: IconType;
export declare const GiCard3Spades: IconType;
export declare const GiCard4Clubs: IconType;
export declare const GiCard4Diamonds: IconType;
export declare const GiCard4Hearts: IconType;
export declare const GiCard4Spades: IconType;
export declare const GiCard5Clubs: IconType;
export declare const GiCard5Diamonds: IconType;
export declare const GiCard5Hearts: IconType;
export declare const GiCard5Spades: IconType;
export declare const GiCard6Clubs: IconType;
export declare const GiCard6Diamonds: IconType;
export declare const GiCard6Hearts: IconType;
export declare const GiCard6Spades: IconType;
export declare const GiCard7Clubs: IconType;
export declare const GiCard7Diamonds: IconType;
export declare const GiCard7Hearts: IconType;
export declare const GiCard7Spades: IconType;
export declare const GiCard8Clubs: IconType;
export declare const GiCard8Diamonds: IconType;
export declare const GiCard8Hearts: IconType;
export declare const GiCard8Spades: IconType;
export declare const GiCard9Clubs: IconType;
export declare const GiCard9Diamonds: IconType;
export declare const GiCard9Hearts: IconType;
export declare const GiCard9Spades: IconType;
export declare const GiCardAceClubs: IconType;
export declare const GiCardAceDiamonds: IconType;
export declare const GiCardAceHearts: IconType;
export declare const GiCardAceSpades: IconType;
export declare const GiCardBurn: IconType;
export declare const GiCardDiscard: IconType;
export declare const GiCardDraw: IconType;
export declare const GiCardExchange: IconType;
export declare const GiCardJackClubs: IconType;
export declare const GiCardJackDiamonds: IconType;
export declare const GiCardJackHearts: IconType;
export declare const GiCardJackSpades: IconType;
export declare const GiCardJoker: IconType;
export declare const GiCardKingClubs: IconType;
export declare const GiCardKingDiamonds: IconType;
export declare const GiCardKingHearts: IconType;
export declare const GiCardKingSpades: IconType;
export declare const GiCardPick: IconType;
export declare const GiCardPickup: IconType;
export declare const GiCardPlay: IconType;
export declare const GiCardQueenClubs: IconType;
export declare const GiCardQueenDiamonds: IconType;
export declare const GiCardQueenHearts: IconType;
export declare const GiCardQueenSpades: IconType;
export declare const GiCardRandom: IconType;
export declare const GiCardboardBoxClosed: IconType;
export declare const GiCardboardBox: IconType;
export declare const GiCargoCrane: IconType;
export declare const GiCargoCrate: IconType;
export declare const GiCargoShip: IconType;
export declare const GiCarillon: IconType;
export declare const GiCarnivalMask: IconType;
export declare const GiCarnivoreMouth: IconType;
export declare const GiCarnivorousPlant: IconType;
export declare const GiCarnyx: IconType;
export declare const GiCarousel: IconType;
export declare const GiCarpetBombing: IconType;
export declare const GiCarrier: IconType;
export declare const GiCarrion: IconType;
export declare const GiCarrot: IconType;
export declare const GiCartwheel: IconType;
export declare const GiCash: IconType;
export declare const GiCassowaryHead: IconType;
export declare const GiCastleRuins: IconType;
export declare const GiCastle: IconType;
export declare const GiCat: IconType;
export declare const GiCatapult: IconType;
export declare const GiCatch: IconType;
export declare const GiCaterpillar: IconType;
export declare const GiCauldron: IconType;
export declare const GiCavalry: IconType;
export declare const GiCaveEntrance: IconType;
export declare const GiCaveman: IconType;
export declare const GiCctvCamera: IconType;
export declare const GiCeilingBarnacle: IconType;
export declare const GiCeilingLight: IconType;
export declare const GiCelebrationFire: IconType;
export declare const GiCellarBarrels: IconType;
export declare const GiCementShoes: IconType;
export declare const GiCentaurHeart: IconType;
export declare const GiCentaur: IconType;
export declare const GiCentipede: IconType;
export declare const GiCenturionHelmet: IconType;
export declare const GiCeremonialMask: IconType;
export declare const GiChainLightning: IconType;
export declare const GiChainMail: IconType;
export declare const GiChainedArrowHeads: IconType;
export declare const GiChainedHeart: IconType;
export declare const GiChaingun: IconType;
export declare const GiChainsaw: IconType;
export declare const GiChakram: IconType;
export declare const GiChaliceDrops: IconType;
export declare const GiChalkOutlineMurder: IconType;
export declare const GiChameleonGlyph: IconType;
export declare const GiChampagneCork: IconType;
export declare const GiChampions: IconType;
export declare const GiChanterelles: IconType;
export declare const GiCharacter: IconType;
export declare const GiCharcuterie: IconType;
export declare const GiChargedArrow: IconType;
export declare const GiChargingBull: IconType;
export declare const GiCharging: IconType;
export declare const GiChariot: IconType;
export declare const GiCharm: IconType;
export declare const GiChart: IconType;
export declare const GiChatBubble: IconType;
export declare const GiCheckMark: IconType;
export declare const GiCheckboxTree: IconType;
export declare const GiCheckedShield: IconType;
export declare const GiCheckeredDiamond: IconType;
export declare const GiCheckeredFlag: IconType;
export declare const GiChecklist: IconType;
export declare const GiCheerful: IconType;
export declare const GiCheeseWedge: IconType;
export declare const GiChefToque: IconType;
export declare const GiChelseaBoot: IconType;
export declare const GiChemicalArrow: IconType;
export declare const GiChemicalBolt: IconType;
export declare const GiChemicalDrop: IconType;
export declare const GiChemicalTank: IconType;
export declare const GiCherish: IconType;
export declare const GiCherry: IconType;
export declare const GiChessBishop: IconType;
export declare const GiChessKing: IconType;
export declare const GiChessKnight: IconType;
export declare const GiChessPawn: IconType;
export declare const GiChessQueen: IconType;
export declare const GiChessRook: IconType;
export declare const GiChestArmor: IconType;
export declare const GiChest: IconType;
export declare const GiChestnutLeaf: IconType;
export declare const GiChewedHeart: IconType;
export declare const GiChewedSkull: IconType;
export declare const GiChickenLeg: IconType;
export declare const GiChickenOven: IconType;
export declare const GiChicken: IconType;
export declare const GiChiliPepper: IconType;
export declare const GiChimney: IconType;
export declare const GiChipsBag: IconType;
export declare const GiChisel: IconType;
export declare const GiChocolateBar: IconType;
export declare const GiChoice: IconType;
export declare const GiChoppedSkull: IconType;
export declare const GiChopsticks: IconType;
export declare const GiChurch: IconType;
export declare const GiCigale: IconType;
export declare const GiCigar: IconType;
export declare const GiCigarette: IconType;
export declare const GiCircleCage: IconType;
export declare const GiCircleClaws: IconType;
export declare const GiCircleForest: IconType;
export declare const GiCircleSparks: IconType;
export declare const GiCircle: IconType;
export declare const GiCirclingFish: IconType;
export declare const GiCircuitry: IconType;
export declare const GiCircularSaw: IconType;
export declare const GiCircularSawblade: IconType;
export declare const GiCityCar: IconType;
export declare const GiClamp: IconType;
export declare const GiClapperboard: IconType;
export declare const GiClarinet: IconType;
export declare const GiClassicalKnowledge: IconType;
export declare const GiClawHammer: IconType;
export declare const GiClawSlashes: IconType;
export declare const GiClawString: IconType;
export declare const GiClaw: IconType;
export declare const GiClaws: IconType;
export declare const GiClayBrick: IconType;
export declare const GiClaymoreExplosive: IconType;
export declare const GiCleaver: IconType;
export declare const GiCleopatra: IconType;
export declare const GiClick: IconType;
export declare const GiCliffCrossing: IconType;
export declare const GiCloakDagger: IconType;
export declare const GiCloak: IconType;
export declare const GiClockwiseRotation: IconType;
export declare const GiClockwork: IconType;
export declare const GiClosedBarbute: IconType;
export declare const GiClosedDoors: IconType;
export declare const GiClothJar: IconType;
export declare const GiClothes: IconType;
export declare const GiClothesline: IconType;
export declare const GiClothespin: IconType;
export declare const GiCloudDownload: IconType;
export declare const GiCloudRing: IconType;
export declare const GiCloudUpload: IconType;
export declare const GiCloudyFork: IconType;
export declare const GiClout: IconType;
export declare const GiCloverSpiked: IconType;
export declare const GiClover: IconType;
export declare const GiClown: IconType;
export declare const GiClownfish: IconType;
export declare const GiClubs: IconType;
export declare const GiClusterBomb: IconType;
export declare const GiCoaDeJima: IconType;
export declare const GiCoalPile: IconType;
export declare const GiCoalWagon: IconType;
export declare const GiCobra: IconType;
export declare const GiCobweb: IconType;
export declare const GiCoconuts: IconType;
export declare const GiCoffeeBeans: IconType;
export declare const GiCoffeeCup: IconType;
export declare const GiCoffeeMug: IconType;
export declare const GiCoffeePot: IconType;
export declare const GiCoffin: IconType;
export declare const GiCogLock: IconType;
export declare const GiCog: IconType;
export declare const GiCogsplosion: IconType;
export declare const GiCoiledNail: IconType;
export declare const GiCoilingCurl: IconType;
export declare const GiCoinflip: IconType;
export declare const GiCoinsPile: IconType;
export declare const GiCoins: IconType;
export declare const GiColdHeart: IconType;
export declare const GiColiseum: IconType;
export declare const GiColombia: IconType;
export declare const GiColombianStatue: IconType;
export declare const GiColtM1911: IconType;
export declare const GiColumnVase: IconType;
export declare const GiComa: IconType;
export declare const GiComb: IconType;
export declare const GiCombinationLock: IconType;
export declare const GiCometSpark: IconType;
export declare const GiCommercialAirplane: IconType;
export declare const GiCompactDisc: IconType;
export declare const GiCompanionCube: IconType;
export declare const GiCompass: IconType;
export declare const GiComputerFan: IconType;
export declare const GiComputing: IconType;
export declare const GiConcentrationOrb: IconType;
export declare const GiConcentricCrescents: IconType;
export declare const GiConcreteBag: IconType;
export declare const GiCondorEmblem: IconType;
export declare const GiCondyluraSkull: IconType;
export declare const GiConfirmed: IconType;
export declare const GiConfrontation: IconType;
export declare const GiCongress: IconType;
export declare const GiConqueror: IconType;
export declare const GiConsoleController: IconType;
export declare const GiContortionist: IconType;
export declare const GiContract: IconType;
export declare const GiControlTower: IconType;
export declare const GiConvergenceTarget: IconType;
export declare const GiConversation: IconType;
export declare const GiConverseShoe: IconType;
export declare const GiConvict: IconType;
export declare const GiConvince: IconType;
export declare const GiConwayLifeGlider: IconType;
export declare const GiCook: IconType;
export declare const GiCookie: IconType;
export declare const GiCookingGlove: IconType;
export declare const GiCookingPot: IconType;
export declare const GiCoolSpices: IconType;
export declare const GiCooler: IconType;
export declare const GiCootieCatcher: IconType;
export declare const GiCoral: IconType;
export declare const GiCorkHat: IconType;
export declare const GiCorkedTube: IconType;
export declare const GiCorkscrew: IconType;
export declare const GiCorn: IconType;
export declare const GiCornerExplosion: IconType;
export declare const GiCornerFlag: IconType;
export declare const GiCornucopia: IconType;
export declare const GiCoronation: IconType;
export declare const GiCorporal: IconType;
export declare const GiCorset: IconType;
export declare const GiCorsica: IconType;
export declare const GiCosmicEgg: IconType;
export declare const GiCottonFlower: IconType;
export declare const GiCoveredJar: IconType;
export declare const GiCow: IconType;
export declare const GiCowboyBoot: IconType;
export declare const GiCowboyHolster: IconType;
export declare const GiCowled: IconType;
export declare const GiCpuShot: IconType;
export declare const GiCpu: IconType;
export declare const GiCrabClaw: IconType;
export declare const GiCrab: IconType;
export declare const GiCrackedAlienSkull: IconType;
export declare const GiCrackedBallDunk: IconType;
export declare const GiCrackedDisc: IconType;
export declare const GiCrackedGlass: IconType;
export declare const GiCrackedHelm: IconType;
export declare const GiCrackedMask: IconType;
export declare const GiCrackedSaber: IconType;
export declare const GiCrackedShield: IconType;
export declare const GiCrafting: IconType;
export declare const GiCrags: IconType;
export declare const GiCrane: IconType;
export declare const GiCreditsCurrency: IconType;
export declare const GiCrenelCrown: IconType;
export declare const GiCrenulatedShield: IconType;
export declare const GiCrescentBlade: IconType;
export declare const GiCrescentStaff: IconType;
export declare const GiCrestedHelmet: IconType;
export declare const GiCricketBat: IconType;
export declare const GiCricket: IconType;
export declare const GiCrimeSceneTape: IconType;
export declare const GiCrocJaws: IconType;
export declare const GiCrocSword: IconType;
export declare const GiCroissant: IconType;
export declare const GiCroissantsPupil: IconType;
export declare const GiCrookFlail: IconType;
export declare const GiCrossFlare: IconType;
export declare const GiCrossMark: IconType;
export declare const GiCrossShield: IconType;
export declare const GiCrossbow: IconType;
export declare const GiCrosscutSaw: IconType;
export declare const GiCrossedAirFlows: IconType;
export declare const GiCrossedAxes: IconType;
export declare const GiCrossedBones: IconType;
export declare const GiCrossedChains: IconType;
export declare const GiCrossedClaws: IconType;
export declare const GiCrossedPistols: IconType;
export declare const GiCrossedSabres: IconType;
export declare const GiCrossedSlashes: IconType;
export declare const GiCrossedSwords: IconType;
export declare const GiCrosshairArrow: IconType;
export declare const GiCrosshair: IconType;
export declare const GiCrossroad: IconType;
export declare const GiCrowDive: IconType;
export declare const GiCrowNest: IconType;
export declare const GiCrowbar: IconType;
export declare const GiCrownCoin: IconType;
export declare const GiCrownOfThorns: IconType;
export declare const GiCrown: IconType;
export declare const GiCrownedExplosion: IconType;
export declare const GiCrownedHeart: IconType;
export declare const GiCrownedSkull: IconType;
export declare const GiCrucifix: IconType;
export declare const GiCruiser: IconType;
export declare const GiCrumblingBall: IconType;
export declare const GiCrush: IconType;
export declare const GiCryoChamber: IconType;
export declare const GiCryptEntrance: IconType;
export declare const GiCrystalBall: IconType;
export declare const GiCrystalBars: IconType;
export declare const GiCrystalCluster: IconType;
export declare const GiCrystalEarrings: IconType;
export declare const GiCrystalEye: IconType;
export declare const GiCrystalGrowth: IconType;
export declare const GiCrystalShine: IconType;
export declare const GiCrystalShrine: IconType;
export declare const GiCrystalWand: IconType;
export declare const GiCrystalize: IconType;
export declare const GiCuauhtli: IconType;
export declare const GiCube: IconType;
export declare const GiCubeforce: IconType;
export declare const GiCubes: IconType;
export declare const GiCuckooClock: IconType;
export declare const GiCultist: IconType;
export declare const GiCupcake: IconType;
export declare const GiCupidonArrow: IconType;
export declare const GiCurledLeaf: IconType;
export declare const GiCurledTentacle: IconType;
export declare const GiCurlingStone: IconType;
export declare const GiCurlingVines: IconType;
export declare const GiCurlyMask: IconType;
export declare const GiCurlyWing: IconType;
export declare const GiCursedStar: IconType;
export declare const GiCurvyKnife: IconType;
export declare const GiCustodianHelmet: IconType;
export declare const GiCutDiamond: IconType;
export declare const GiCutLemon: IconType;
export declare const GiCutPalm: IconType;
export declare const GiCyberEye: IconType;
export declare const GiCyborgFace: IconType;
export declare const GiCycle: IconType;
export declare const GiCycling: IconType;
export declare const GiCyclops: IconType;
export declare const GiCzSkorpion: IconType;
export declare const GiD10: IconType;
export declare const GiD12: IconType;
export declare const GiD4: IconType;
export declare const GiDaemonPull: IconType;
export declare const GiDaemonSkull: IconType;
export declare const GiDaggerRose: IconType;
export declare const GiDaggers: IconType;
export declare const GiDaisy: IconType;
export declare const GiDam: IconType;
export declare const GiDamagedHouse: IconType;
export declare const GiDandelionFlower: IconType;
export declare const GiDango: IconType;
export declare const GiDarkSquad: IconType;
export declare const GiDart: IconType;
export declare const GiDatabase: IconType;
export declare const GiDeadEye: IconType;
export declare const GiDeadHead: IconType;
export declare const GiDeadWood: IconType;
export declare const GiDeadlyStrike: IconType;
export declare const GiDeathJuice: IconType;
export declare const GiDeathNote: IconType;
export declare const GiDeathSkull: IconType;
export declare const GiDeathStar: IconType;
export declare const GiDeathZone: IconType;
export declare const GiDeathcab: IconType;
export declare const GiDecapitation: IconType;
export declare const GiDeerHead: IconType;
export declare const GiDeerTrack: IconType;
export declare const GiDeer: IconType;
export declare const GiDefenseSatellite: IconType;
export declare const GiDefensiveWall: IconType;
export declare const GiDefibrilate: IconType;
export declare const GiDekuTree: IconType;
export declare const GiDelicatePerfume: IconType;
export declare const GiDelighted: IconType;
export declare const GiDeliveryDrone: IconType;
export declare const GiDemolish: IconType;
export declare const GiDervishSwords: IconType;
export declare const GiDesertEagle: IconType;
export declare const GiDesertSkull: IconType;
export declare const GiDesert: IconType;
export declare const GiDeshretRedCrown: IconType;
export declare const GiDeskLamp: IconType;
export declare const GiDesk: IconType;
export declare const GiDespair: IconType;
export declare const GiDetonator: IconType;
export declare const GiDetour: IconType;
export declare const GiDevilMask: IconType;
export declare const GiDew: IconType;
export declare const GiDiabloSkull: IconType;
export declare const GiDiagram: IconType;
export declare const GiDialPadlock: IconType;
export declare const GiDiamondHard: IconType;
export declare const GiDiamondHilt: IconType;
export declare const GiDiamondRing: IconType;
export declare const GiDiamondTrophy: IconType;
export declare const GiDiamondsSmile: IconType;
export declare const GiDiamonds: IconType;
export declare const GiDiceEightFacesEight: IconType;
export declare const GiDiceFire: IconType;
export declare const GiDiceShield: IconType;
export declare const GiDiceSixFacesFive: IconType;
export declare const GiDiceSixFacesFour: IconType;
export declare const GiDiceSixFacesOne: IconType;
export declare const GiDiceSixFacesSix: IconType;
export declare const GiDiceSixFacesThree: IconType;
export declare const GiDiceSixFacesTwo: IconType;
export declare const GiDiceTarget: IconType;
export declare const GiDiceTwentyFacesOne: IconType;
export declare const GiDiceTwentyFacesTwenty: IconType;
export declare const GiDigDug: IconType;
export declare const GiDigHole: IconType;
export declare const GiDigitalTrace: IconType;
export declare const GiDimetrodon: IconType;
export declare const GiDinosaurBones: IconType;
export declare const GiDinosaurEgg: IconType;
export declare const GiDinosaurRex: IconType;
export declare const GiDiplodocus: IconType;
export declare const GiDiploma: IconType;
export declare const GiDirectionSign: IconType;
export declare const GiDirectionSigns: IconType;
export declare const GiDirectorChair: IconType;
export declare const GiDirewolf: IconType;
export declare const GiDiscGolfBag: IconType;
export declare const GiDiscGolfBasket: IconType;
export declare const GiDisc: IconType;
export declare const GiDiscobolus: IconType;
export declare const GiDiscussion: IconType;
export declare const GiDisintegrate: IconType;
export declare const GiDistraction: IconType;
export declare const GiDistressSignal: IconType;
export declare const GiDivergence: IconType;
export declare const GiDivert: IconType;
export declare const GiDividedSpiral: IconType;
export declare const GiDividedSquare: IconType;
export declare const GiDivingDagger: IconType;
export declare const GiDivingHelmet: IconType;
export declare const GiDjedPillar: IconType;
export declare const GiDjembe: IconType;
export declare const GiDjinn: IconType;
export declare const GiDna1: IconType;
export declare const GiDna2: IconType;
export declare const GiDoctorFace: IconType;
export declare const GiDodge: IconType;
export declare const GiDodging: IconType;
export declare const GiDogBowl: IconType;
export declare const GiDogHouse: IconType;
export declare const GiDolmen: IconType;
export declare const GiDolphin: IconType;
export declare const GiDominoMask: IconType;
export declare const GiDominoTiles: IconType;
export declare const GiDonerKebab: IconType;
export declare const GiDonkey: IconType;
export declare const GiDonut: IconType;
export declare const GiDoorHandle: IconType;
export declare const GiDoorRingHandle: IconType;
export declare const GiDoorWatcher: IconType;
export declare const GiDoor: IconType;
export declare const GiDoorway: IconType;
export declare const GiDorsalScales: IconType;
export declare const GiDoubleDiaphragm: IconType;
export declare const GiDoubleDragon: IconType;
export declare const GiDoubleFaceMask: IconType;
export declare const GiDoubleFish: IconType;
export declare const GiDoubleNecklace: IconType;
export declare const GiDoubleQuaver: IconType;
export declare const GiDoubleRingedOrb: IconType;
export declare const GiDoubleShot: IconType;
export declare const GiDoubleStreetLights: IconType;
export declare const GiDoubled: IconType;
export declare const GiDoughRoller: IconType;
export declare const GiDove: IconType;
export declare const GiDozen: IconType;
export declare const GiDragonBalls: IconType;
export declare const GiDragonBreath: IconType;
export declare const GiDragonHead: IconType;
export declare const GiDragonOrb: IconType;
export declare const GiDragonShield: IconType;
export declare const GiDragonSpiral: IconType;
export declare const GiDragonfly: IconType;
export declare const GiDrakkarDragon: IconType;
export declare const GiDrakkar: IconType;
export declare const GiDramaMasks: IconType;
export declare const GiDrawbridge: IconType;
export declare const GiDreadSkull: IconType;
export declare const GiDread: IconType;
export declare const GiDreadnought: IconType;
export declare const GiDreamCatcher: IconType;
export declare const GiDress: IconType;
export declare const GiDrill: IconType;
export declare const GiDrinkMe: IconType;
export declare const GiDrinking: IconType;
export declare const GiDrippingBlade: IconType;
export declare const GiDrippingGoo: IconType;
export declare const GiDrippingHoney: IconType;
export declare const GiDrippingKnife: IconType;
export declare const GiDrippingStar: IconType;
export declare const GiDrippingStone: IconType;
export declare const GiDrippingSword: IconType;
export declare const GiDrippingTube: IconType;
export declare const GiDropEarrings: IconType;
export declare const GiDropWeapon: IconType;
export declare const GiDrop: IconType;
export declare const GiDropletSplash: IconType;
export declare const GiDroplets: IconType;
export declare const GiDrowning: IconType;
export declare const GiDrumKit: IconType;
export declare const GiDrum: IconType;
export declare const GiDualityMask: IconType;
export declare const GiDuality: IconType;
export declare const GiDuckPalm: IconType;
export declare const GiDuck: IconType;
export declare const GiDuel: IconType;
export declare const GiDuffelBag: IconType;
export declare const GiDumplingBao: IconType;
export declare const GiDumpling: IconType;
export declare const GiDunceCap: IconType;
export declare const GiDungeonGate: IconType;
export declare const GiDungeonLight: IconType;
export declare const GiDuration: IconType;
export declare const GiDustCloud: IconType;
export declare const GiDutchBike: IconType;
export declare const GiDwarfFace: IconType;
export declare const GiDwarfHelmet: IconType;
export declare const GiDwarfKing: IconType;
export declare const GiDwennimmen: IconType;
export declare const GiDynamite: IconType;
export declare const GiEagleEmblem: IconType;
export declare const GiEagleHead: IconType;
export declare const GiEarbuds: IconType;
export declare const GiEarrings: IconType;
export declare const GiEarthAfricaEurope: IconType;
export declare const GiEarthAmerica: IconType;
export declare const GiEarthAsiaOceania: IconType;
export declare const GiEarthCrack: IconType;
export declare const GiEarthSpit: IconType;
export declare const GiEarthWorm: IconType;
export declare const GiEarwig: IconType;
export declare const GiEasel: IconType;
export declare const GiEasterEgg: IconType;
export declare const GiEatingPelican: IconType;
export declare const GiEating: IconType;
export declare const GiEchoRipples: IconType;
export declare const GiEclipseFlare: IconType;
export declare const GiEclipseSaw: IconType;
export declare const GiEclipse: IconType;
export declare const GiEcology: IconType;
export declare const GiEdgeCrack: IconType;
export declare const GiEdgedShield: IconType;
export declare const GiEel: IconType;
export declare const GiEggClutch: IconType;
export declare const GiEggDefense: IconType;
export declare const GiEggEye: IconType;
export declare const GiEggPod: IconType;
export declare const GiEgypt: IconType;
export declare const GiEgyptianBird: IconType;
export declare const GiEgyptianProfile: IconType;
export declare const GiEgyptianPyramids: IconType;
export declare const GiEgyptianSphinx: IconType;
export declare const GiEgyptianTemple: IconType;
export declare const GiEgyptianUrns: IconType;
export declare const GiEgyptianWalk: IconType;
export declare const GiEightBall: IconType;
export declare const GiElbowPad: IconType;
export declare const GiElderberry: IconType;
export declare const GiElectricWhip: IconType;
export declare const GiElectric: IconType;
export declare const GiElectricalCrescent: IconType;
export declare const GiElectricalResistance: IconType;
export declare const GiElectricalSocket: IconType;
export declare const GiElephantHead: IconType;
export declare const GiElephant: IconType;
export declare const GiElevator: IconType;
export declare const GiElfEar: IconType;
export declare const GiElfHelmet: IconType;
export declare const GiElvenCastle: IconType;
export declare const GiEmberShot: IconType;
export declare const GiEmbrassedEnergy: IconType;
export declare const GiEmbryo: IconType;
export declare const GiEmeraldNecklace: IconType;
export declare const GiEmerald: IconType;
export declare const GiEmptyChessboard: IconType;
export declare const GiEmptyHourglass: IconType;
export declare const GiEmptyMetalBucketHandle: IconType;
export declare const GiEmptyMetalBucket: IconType;
export declare const GiEmptyWoodBucketHandle: IconType;
export declare const GiEmptyWoodBucket: IconType;
export declare const GiEncirclement: IconType;
export declare const GiEnergise: IconType;
export declare const GiEnergyArrow: IconType;
export declare const GiEnergyBreath: IconType;
export declare const GiEnergyShield: IconType;
export declare const GiEnergySword: IconType;
export declare const GiEnergyTank: IconType;
export declare const GiEngagementRing: IconType;
export declare const GiEnlightenment: IconType;
export declare const GiEnrage: IconType;
export declare const GiEntMouth: IconType;
export declare const GiEntangledTyphoon: IconType;
export declare const GiEntryDoor: IconType;
export declare const GiEnvelope: IconType;
export declare const GiErlenmeyer: IconType;
export declare const GiErmine: IconType;
export declare const GiEruption: IconType;
export declare const GiEscalator: IconType;
export declare const GiEskimo: IconType;
export declare const GiEternalLove: IconType;
export declare const GiEuropeanFlag: IconType;
export declare const GiEvasion: IconType;
export declare const GiEvilBat: IconType;
export declare const GiEvilBook: IconType;
export declare const GiEvilBud: IconType;
export declare const GiEvilComet: IconType;
export declare const GiEvilEyes: IconType;
export declare const GiEvilFork: IconType;
export declare const GiEvilHand: IconType;
export declare const GiEvilLove: IconType;
export declare const GiEvilMinion: IconType;
export declare const GiEvilMoon: IconType;
export declare const GiEvilTower: IconType;
export declare const GiEvilTree: IconType;
export declare const GiEvilWings: IconType;
export declare const GiExecutionerHood: IconType;
export declare const GiExitDoor: IconType;
export declare const GiExpand: IconType;
export declare const GiExpandedRays: IconType;
export declare const GiExpander: IconType;
export declare const GiExpense: IconType;
export declare const GiExplodingPlanet: IconType;
export declare const GiExplosionRays: IconType;
export declare const GiExplosiveMaterials: IconType;
export declare const GiExplosiveMeeting: IconType;
export declare const GiExtraLucid: IconType;
export declare const GiExtraTime: IconType;
export declare const GiExtractionOrb: IconType;
export declare const GiEyeOfHorus: IconType;
export declare const GiEyeShield: IconType;
export declare const GiEyeTarget: IconType;
export declare const GiEyeball: IconType;
export declare const GiEyedropper: IconType;
export declare const GiEyelashes: IconType;
export declare const GiEyepatch: IconType;
export declare const GiEyestalk: IconType;
export declare const GiFClef: IconType;
export declare const GiF1Car: IconType;
export declare const GiFaceToFace: IconType;
export declare const GiFactoryArm: IconType;
export declare const GiFactory: IconType;
export declare const GiFairyWand: IconType;
export declare const GiFairyWings: IconType;
export declare const GiFairy: IconType;
export declare const GiFalconMoon: IconType;
export declare const GiFallDown: IconType;
export declare const GiFallingBlob: IconType;
export declare const GiFallingBomb: IconType;
export declare const GiFallingBoulder: IconType;
export declare const GiFallingEye: IconType;
export declare const GiFallingLeaf: IconType;
export declare const GiFallingOvoid: IconType;
export declare const GiFallingRocks: IconType;
export declare const GiFallingStar: IconType;
export declare const GiFalling: IconType;
export declare const GiFalloutShelter: IconType;
export declare const GiFamas: IconType;
export declare const GiFamilyHouse: IconType;
export declare const GiFamilyTree: IconType;
export declare const GiFangedSkull: IconType;
export declare const GiFangsCircle: IconType;
export declare const GiFangs: IconType;
export declare const GiFarmTractor: IconType;
export declare const GiFarmer: IconType;
export declare const GiFastArrow: IconType;
export declare const GiFastBackwardButton: IconType;
export declare const GiFastForwardButton: IconType;
export declare const GiFastNoodles: IconType;
export declare const GiFat: IconType;
export declare const GiFeatherNecklace: IconType;
export declare const GiFeatherWound: IconType;
export declare const GiFeather: IconType;
export declare const GiFeatheredWing: IconType;
export declare const GiFedora: IconType;
export declare const GiFeline: IconType;
export declare const GiFemaleLegs: IconType;
export declare const GiFemaleVampire: IconType;
export declare const GiFemale: IconType;
export declare const GiFencer: IconType;
export declare const GiFern: IconType;
export declare const GiFertilizerBag: IconType;
export declare const GiFetus: IconType;
export declare const GiFez: IconType;
export declare const GiFieldGun: IconType;
export declare const GiField: IconType;
export declare const GiFigurehead: IconType;
export declare const GiFiles: IconType;
export declare const GiFilmProjector: IconType;
export declare const GiFilmSpool: IconType;
export declare const GiFilmStrip: IconType;
export declare const GiFinch: IconType;
export declare const GiFingerPrint: IconType;
export declare const GiFingernail: IconType;
export declare const GiFingersCrossed: IconType;
export declare const GiFinishLine: IconType;
export declare const GiFireAce: IconType;
export declare const GiFireAxe: IconType;
export declare const GiFireBomb: IconType;
export declare const GiFireBottle: IconType;
export declare const GiFireBowl: IconType;
export declare const GiFireBreath: IconType;
export declare const GiFireDash: IconType;
export declare const GiFireExtinguisher: IconType;
export declare const GiFireFlower: IconType;
export declare const GiFireGem: IconType;
export declare const GiFirePunch: IconType;
export declare const GiFireRay: IconType;
export declare const GiFireRing: IconType;
export declare const GiFireShield: IconType;
export declare const GiFireShrine: IconType;
export declare const GiFireSilhouette: IconType;
export declare const GiFireSpellCast: IconType;
export declare const GiFireTail: IconType;
export declare const GiFireWave: IconType;
export declare const GiFireZone: IconType;
export declare const GiFire: IconType;
export declare const GiFireball: IconType;
export declare const GiFireflake: IconType;
export declare const GiFireplace: IconType;
export declare const GiFirewall: IconType;
export declare const GiFireworkRocket: IconType;
export declare const GiFirstAidKit: IconType;
export declare const GiFishBucket: IconType;
export declare const GiFishCooked: IconType;
export declare const GiFishCorpse: IconType;
export declare const GiFishEggs: IconType;
export declare const GiFishEscape: IconType;
export declare const GiFishMonster: IconType;
export declare const GiFishScales: IconType;
export declare const GiFishSmoking: IconType;
export declare const GiFishbone: IconType;
export declare const GiFishhookFork: IconType;
export declare const GiFishingBoat: IconType;
export declare const GiFishingHook: IconType;
export declare const GiFishingJig: IconType;
export declare const GiFishingLure: IconType;
export declare const GiFishingNet: IconType;
export declare const GiFishingPole: IconType;
export declare const GiFishingSpoon: IconType;
export declare const GiFishing: IconType;
export declare const GiFission: IconType;
export declare const GiFist: IconType;
export declare const GiFizzingFlask: IconType;
export declare const GiFlagObjective: IconType;
export declare const GiFlail: IconType;
export declare const GiFlake: IconType;
export declare const GiFlameClaws: IconType;
export declare const GiFlameSpin: IconType;
export declare const GiFlameTunnel: IconType;
export declare const GiFlame: IconType;
export declare const GiFlamedLeaf: IconType;
export declare const GiFlamer: IconType;
export declare const GiFlamethrowerSoldier: IconType;
export declare const GiFlamethrower: IconType;
export declare const GiFlamingArrow: IconType;
export declare const GiFlamingClaw: IconType;
export declare const GiFlamingSheet: IconType;
export declare const GiFlamingTrident: IconType;
export declare const GiFlamingo: IconType;
export declare const GiFlangedMace: IconType;
export declare const GiFlashGrenade: IconType;
export declare const GiFlashlight: IconType;
export declare const GiFlatHammer: IconType;
export declare const GiFlatPawPrint: IconType;
export declare const GiFlatPlatform: IconType;
export declare const GiFlatStar: IconType;
export declare const GiFlatTire: IconType;
export declare const GiFlatbedCovered: IconType;
export declare const GiFlatbed: IconType;
export declare const GiFlatfish: IconType;
export declare const GiFlax: IconType;
export declare const GiFleshyMass: IconType;
export declare const GiFleurDeLys: IconType;
export declare const GiFlexibleLamp: IconType;
export declare const GiFlexibleStar: IconType;
export declare const GiFlintSpark: IconType;
export declare const GiFlipFlops: IconType;
export declare const GiFloatingCrystal: IconType;
export declare const GiFloatingGhost: IconType;
export declare const GiFloatingPlatforms: IconType;
export declare const GiFloatingTentacles: IconType;
export declare const GiFlood: IconType;
export declare const GiFloorHatch: IconType;
export declare const GiFloorPolisher: IconType;
export declare const GiFlour: IconType;
export declare const GiFlowerEmblem: IconType;
export declare const GiFlowerHat: IconType;
export declare const GiFlowerPot: IconType;
export declare const GiFlowerStar: IconType;
export declare const GiFlowerTwirl: IconType;
export declare const GiFlowers: IconType;
export declare const GiFluffyCloud: IconType;
export declare const GiFluffyFlame: IconType;
export declare const GiFluffySwirl: IconType;
export declare const GiFluffyTrefoil: IconType;
export declare const GiFluffyWing: IconType;
export declare const GiFlute: IconType;
export declare const GiFly: IconType;
export declare const GiFlyingBeetle: IconType;
export declare const GiFlyingDagger: IconType;
export declare const GiFlyingFlag: IconType;
export declare const GiFlyingFox: IconType;
export declare const GiFlyingShuriken: IconType;
export declare const GiFlyingTarget: IconType;
export declare const GiFlyingTrout: IconType;
export declare const GiFnFal: IconType;
export declare const GiFoam: IconType;
export declare const GiFoamyDisc: IconType;
export declare const GiFocusedLightning: IconType;
export declare const GiFogLight: IconType;
export declare const GiFog: IconType;
export declare const GiFoldedPaper: IconType;
export declare const GiFomorian: IconType;
export declare const GiFoodChain: IconType;
export declare const GiFoodTruck: IconType;
export declare const GiFootPlaster: IconType;
export declare const GiFootTrip: IconType;
export declare const GiFootprint: IconType;
export declare const GiFootsteps: IconType;
export declare const GiFootyField: IconType;
export declare const GiForearm: IconType;
export declare const GiForestCamp: IconType;
export declare const GiForestEntrance: IconType;
export declare const GiForest: IconType;
export declare const GiForkKnifeSpoon: IconType;
export declare const GiForklift: IconType;
export declare const GiForwardField: IconType;
export declare const GiForwardSun: IconType;
export declare const GiFossil: IconType;
export declare const GiFoundryBucket: IconType;
export declare const GiFountainPen: IconType;
export declare const GiFountain: IconType;
export declare const GiFoxHead: IconType;
export declare const GiFoxTail: IconType;
export declare const GiFox: IconType;
export declare const GiFragmentedMeteor: IconType;
export declare const GiFragmentedSword: IconType;
export declare const GiFragrance: IconType;
export declare const GiFrance: IconType;
export declare const GiFrankensteinCreature: IconType;
export declare const GiFrayedArrow: IconType;
export declare const GiFreedomDove: IconType;
export declare const GiFreemasonry: IconType;
export declare const GiFrenchFries: IconType;
export declare const GiFrenchHorn: IconType;
export declare const GiFriedEggs: IconType;
export declare const GiFriedFish: IconType;
export declare const GiFrisbee: IconType;
export declare const GiFroeAndMallet: IconType;
export declare const GiFrogFoot: IconType;
export declare const GiFrogPrince: IconType;
export declare const GiFrog: IconType;
export declare const GiFrontTeeth: IconType;
export declare const GiFrontalLobe: IconType;
export declare const GiFrostfire: IconType;
export declare const GiFrozenArrow: IconType;
export declare const GiFrozenBlock: IconType;
export declare const GiFrozenBody: IconType;
export declare const GiFrozenOrb: IconType;
export declare const GiFrozenRing: IconType;
export declare const GiFruitBowl: IconType;
export declare const GiFruitTree: IconType;
export declare const GiFruiting: IconType;
export declare const GiFuelTank: IconType;
export declare const GiFuji: IconType;
export declare const GiFulguroPunch: IconType;
export declare const GiFullFolder: IconType;
export declare const GiFullMetalBucketHandle: IconType;
export declare const GiFullMetalBucket: IconType;
export declare const GiFullMotorcycleHelmet: IconType;
export declare const GiFullPizza: IconType;
export declare const GiFullWoodBucketHandle: IconType;
export declare const GiFullWoodBucket: IconType;
export declare const GiFunnel: IconType;
export declare const GiFurBoot: IconType;
export declare const GiFurShirt: IconType;
export declare const GiFurnace: IconType;
export declare const GiGClef: IconType;
export declare const GiGalaxy: IconType;
export declare const GiGalea: IconType;
export declare const GiGalleon: IconType;
export declare const GiGalley: IconType;
export declare const GiGameConsole: IconType;
export declare const GiGamepadCross: IconType;
export declare const GiGamepad: IconType;
export declare const GiGardeningShears: IconType;
export declare const GiGargoyle: IconType;
export declare const GiGarlic: IconType;
export declare const GiGasMask: IconType;
export declare const GiGasPump: IconType;
export declare const GiGasStove: IconType;
export declare const GiGate: IconType;
export declare const GiGaulsHelm: IconType;
export declare const GiGauntlet: IconType;
export declare const GiGavel: IconType;
export declare const GiGaze: IconType;
export declare const GiGearHammer: IconType;
export declare const GiGearStickPattern: IconType;
export declare const GiGearStick: IconType;
export declare const GiGears: IconType;
export declare const GiGecko: IconType;
export declare const GiGemChain: IconType;
export declare const GiGemNecklace: IconType;
export declare const GiGemPendant: IconType;
export declare const GiGemini: IconType;
export declare const GiGems: IconType;
export declare const GiGhostAlly: IconType;
export declare const GiGhost: IconType;
export declare const GiGiantSquid: IconType;
export declare const GiGiant: IconType;
export declare const GiGibbet: IconType;
export declare const GiGiftOfKnowledge: IconType;
export declare const GiGiftTrap: IconType;
export declare const GiGingerbreadMan: IconType;
export declare const GiGinkgoLeaf: IconType;
export declare const GiGladius: IconType;
export declare const GiGlaive: IconType;
export declare const GiGlassBall: IconType;
export declare const GiGlassCelebration: IconType;
export declare const GiGlassHeart: IconType;
export declare const GiGlassShot: IconType;
export declare const GiGlider: IconType;
export declare const GiGlobeRing: IconType;
export declare const GiGlobe: IconType;
export declare const GiGlock: IconType;
export declare const GiGloop: IconType;
export declare const GiGloves: IconType;
export declare const GiGlowingArtifact: IconType;
export declare const GiGlowingHands: IconType;
export declare const GiGluttonousSmile: IconType;
export declare const GiGluttony: IconType;
export declare const GiGoalKeeper: IconType;
export declare const GiGoat: IconType;
export declare const GiGoblinCamp: IconType;
export declare const GiGoblinHead: IconType;
export declare const GiGoldBar: IconType;
export declare const GiGoldMine: IconType;
export declare const GiGoldNuggets: IconType;
export declare const GiGoldScarab: IconType;
export declare const GiGoldShell: IconType;
export declare const GiGoldStack: IconType;
export declare const GiGolemHead: IconType;
export declare const GiGolfFlag: IconType;
export declare const GiGolfTee: IconType;
export declare const GiGong: IconType;
export declare const GiGooExplosion: IconType;
export declare const GiGooSkull: IconType;
export declare const GiGooSpurt: IconType;
export declare const GiGooeyDaemon: IconType;
export declare const GiGooeyEyedSun: IconType;
export declare const GiGooeyImpact: IconType;
export declare const GiGooeyMolecule: IconType;
export declare const GiGooeySword: IconType;
export declare const GiGoose: IconType;
export declare const GiGorilla: IconType;
export declare const GiGothicCross: IconType;
export declare const GiGps: IconType;
export declare const GiGrab: IconType;
export declare const GiGraduateCap: IconType;
export declare const GiGrainBundle: IconType;
export declare const GiGrain: IconType;
export declare const GiGranary: IconType;
export declare const GiGrandPiano: IconType;
export declare const GiGrapes: IconType;
export declare const GiGrapple: IconType;
export declare const GiGraspingClaws: IconType;
export declare const GiGraspingSlug: IconType;
export declare const GiGrassMushroom: IconType;
export declare const GiGrass: IconType;
export declare const GiGraveFlowers: IconType;
export declare const GiGraveyard: IconType;
export declare const GiGreaseTrap: IconType;
export declare const GiGreatPyramid: IconType;
export declare const GiGreatWarTank: IconType;
export declare const GiGreaves: IconType;
export declare const GiGreekSphinx: IconType;
export declare const GiGreekTemple: IconType;
export declare const GiGreenPower: IconType;
export declare const GiGreenhouse: IconType;
export declare const GiGrenade: IconType;
export declare const GiGriffinShield: IconType;
export declare const GiGriffinSymbol: IconType;
export declare const GiGrimReaper: IconType;
export declare const GiGroundSprout: IconType;
export declare const GiGroundbreaker: IconType;
export declare const GiGroupedDrops: IconType;
export declare const GiGrowth: IconType;
export declare const GiGuardedTower: IconType;
export declare const GiGuards: IconType;
export declare const GiGuatemala: IconType;
export declare const GiGuillotine: IconType;
export declare const GiGuitarBassHead: IconType;
export declare const GiGuitarHead: IconType;
export declare const GiGuitar: IconType;
export declare const GiGunRose: IconType;
export declare const GiGunStock: IconType;
export declare const GiGunshot: IconType;
export declare const GiGymBag: IconType;
export declare const GiH2O: IconType;
export declare const GiHabitatDome: IconType;
export declare const GiHadesSymbol: IconType;
export declare const GiHairStrands: IconType;
export declare const GiHalberdShuriken: IconType;
export declare const GiHalberd: IconType;
export declare const GiHalfBodyCrawling: IconType;
export declare const GiHalfDead: IconType;
export declare const GiHalfHeart: IconType;
export declare const GiHalfLog: IconType;
export declare const GiHalfTornado: IconType;
export declare const GiHalt: IconType;
export declare const GiHamShank: IconType;
export declare const GiHamburgerMenu: IconType;
export declare const GiHamburger: IconType;
export declare const GiHammerBreak: IconType;
export declare const GiHammerDrop: IconType;
export declare const GiHammerNails: IconType;
export declare const GiHammerSickle: IconType;
export declare const GiHandBag: IconType;
export declare const GiHandBandage: IconType;
export declare const GiHandGrip: IconType;
export declare const GiHandOfGod: IconType;
export declare const GiHandOk: IconType;
export declare const GiHandSaw: IconType;
export declare const GiHandTruck: IconType;
export declare const GiHandWing: IconType;
export declare const GiHand: IconType;
export declare const GiHandcuffed: IconType;
export declare const GiHandcuffs: IconType;
export declare const GiHandheldFan: IconType;
export declare const GiHangGlider: IconType;
export declare const GiHanger: IconType;
export declare const GiHangingSign: IconType;
export declare const GiHangingSpider: IconType;
export declare const GiHappySkull: IconType;
export declare const GiHarborDock: IconType;
export declare const GiHarp: IconType;
export declare const GiHarpoonChain: IconType;
export declare const GiHarpoonTrident: IconType;
export declare const GiHarpy: IconType;
export declare const GiHarryPotterSkull: IconType;
export declare const GiHastyGrave: IconType;
export declare const GiHatchet: IconType;
export declare const GiHatchets: IconType;
export declare const GiHaunting: IconType;
export declare const GiHawkEmblem: IconType;
export declare const GiHazardSign: IconType;
export declare const GiHazmatSuit: IconType;
export declare const GiHeadShot: IconType;
export declare const GiHeadbandKnot: IconType;
export declare const GiHeadphones: IconType;
export declare const GiHeadshot: IconType;
export declare const GiHealingShield: IconType;
export declare const GiHealing: IconType;
export declare const GiHealthCapsule: IconType;
export declare const GiHealthDecrease: IconType;
export declare const GiHealthIncrease: IconType;
export declare const GiHealthNormal: IconType;
export declare const GiHealthPotion: IconType;
export declare const GiHearingDisabled: IconType;
export declare const GiHeartArmor: IconType;
export declare const GiHeartBattery: IconType;
export declare const GiHeartBeats: IconType;
export declare const GiHeartBottle: IconType;
export declare const GiHeartDrop: IconType;
export declare const GiHeartEarrings: IconType;
export declare const GiHeartInside: IconType;
export declare const GiHeartKey: IconType;
export declare const GiHeartMinus: IconType;
export declare const GiHeartNecklace: IconType;
export declare const GiHeartOrgan: IconType;
export declare const GiHeartPlus: IconType;
export declare const GiHeartShield: IconType;
export declare const GiHeartStake: IconType;
export declare const GiHeartTower: IconType;
export declare const GiHeartWings: IconType;
export declare const GiHeartburn: IconType;
export declare const GiHearts: IconType;
export declare const GiHeatHaze: IconType;
export declare const GiHeavenGate: IconType;
export declare const GiHeavyArrow: IconType;
export declare const GiHeavyBullets: IconType;
export declare const GiHeavyCollar: IconType;
export declare const GiHeavyFall: IconType;
export declare const GiHeavyFighter: IconType;
export declare const GiHeavyHelm: IconType;
export declare const GiHeavyLightning: IconType;
export declare const GiHeavyRain: IconType;
export declare const GiHeavyThornyTriskelion: IconType;
export declare const GiHeavyTimer: IconType;
export declare const GiHedgehog: IconType;
export declare const GiHedjetWhiteCrown: IconType;
export declare const GiHelicoprion: IconType;
export declare const GiHelicopterTail: IconType;
export declare const GiHelicopter: IconType;
export declare const GiHellCrosses: IconType;
export declare const GiHelmetHeadShot: IconType;
export declare const GiHelmet: IconType;
export declare const GiHelp: IconType;
export declare const GiHemp: IconType;
export declare const GiHeptagram: IconType;
export declare const GiHeraldicSun: IconType;
export declare const GiHerbsBundle: IconType;
export declare const GiHeron: IconType;
export declare const GiHexagonalNut: IconType;
export declare const GiHexes: IconType;
export declare const GiHidden: IconType;
export declare const GiHieroglyphLegs: IconType;
export declare const GiHieroglyphY: IconType;
export declare const GiHighFive: IconType;
export declare const GiHighGrass: IconType;
export declare const GiHighHeel: IconType;
export declare const GiHighKick: IconType;
export declare const GiHighPunch: IconType;
export declare const GiHighShot: IconType;
export declare const GiHighTide: IconType;
export declare const GiHighlighter: IconType;
export declare const GiHiking: IconType;
export declare const GiHillConquest: IconType;
export declare const GiHillFort: IconType;
export declare const GiHills: IconType;
export declare const GiHistogram: IconType;
export declare const GiHiveMind: IconType;
export declare const GiHive: IconType;
export declare const GiHobbitDoor: IconType;
export declare const GiHobbitDwelling: IconType;
export declare const GiHockey: IconType;
export declare const GiHoleLadder: IconType;
export declare const GiHole: IconType;
export declare const GiHollowCat: IconType;
export declare const GiHolosphere: IconType;
export declare const GiHolyGrail: IconType;
export declare const GiHolyHandGrenade: IconType;
export declare const GiHolyOak: IconType;
export declare const GiHolySymbol: IconType;
export declare const GiHolyWater: IconType;
export declare const GiHomeGarage: IconType;
export declare const GiHoneyJar: IconType;
export declare const GiHoneycomb: IconType;
export declare const GiHoneypot: IconType;
export declare const GiHood: IconType;
export declare const GiHoodedAssassin: IconType;
export declare const GiHoodedFigure: IconType;
export declare const GiHoodie: IconType;
export declare const GiHoof: IconType;
export declare const GiHook: IconType;
export declare const GiHops: IconType;
export declare const GiHorizonRoad: IconType;
export declare const GiHorizontalFlip: IconType;
export declare const GiHornInternal: IconType;
export declare const GiHornedHelm: IconType;
export declare const GiHornedReptile: IconType;
export declare const GiHornedSkull: IconType;
export declare const GiHorseHead: IconType;
export declare const GiHorseshoe: IconType;
export declare const GiHorus: IconType;
export declare const GiHospitalCross: IconType;
export declare const GiHospital: IconType;
export declare const GiHotDog: IconType;
export declare const GiHotMeal: IconType;
export declare const GiHotSpices: IconType;
export declare const GiHotSurface: IconType;
export declare const GiHound: IconType;
export declare const GiHourglass: IconType;
export declare const GiHouseKeys: IconType;
export declare const GiHouse: IconType;
export declare const GiHumanCannonball: IconType;
export declare const GiHumanEar: IconType;
export declare const GiHumanPyramid: IconType;
export declare const GiHumanTarget: IconType;
export declare const GiHummingbird: IconType;
export declare const GiHungary: IconType;
export declare const GiHunterEyes: IconType;
export declare const GiHuntingBolas: IconType;
export declare const GiHuntingHorn: IconType;
export declare const GiHut: IconType;
export declare const GiHutsVillage: IconType;
export declare const GiHydraShot: IconType;
export declare const GiHydra: IconType;
export declare const GiHyenaHead: IconType;
export declare const GiHypersonicBolt: IconType;
export declare const GiHypersonicMelon: IconType;
export declare const GiHypodermicTest: IconType;
export declare const GiIBeam: IconType;
export declare const GiIBrick: IconType;
export declare const GiIbis: IconType;
export declare const GiIcarus: IconType;
export declare const GiIceBolt: IconType;
export declare const GiIceBomb: IconType;
export declare const GiIceCreamCone: IconType;
export declare const GiIceCreamScoop: IconType;
export declare const GiIceCube: IconType;
export declare const GiIceCubes: IconType;
export declare const GiIceGolem: IconType;
export declare const GiIceIris: IconType;
export declare const GiIcePop: IconType;
export declare const GiIceShield: IconType;
export declare const GiIceSkate: IconType;
export declare const GiIceSpear: IconType;
export declare const GiIceSpellCast: IconType;
export declare const GiIceberg: IconType;
export declare const GiIcebergs: IconType;
export declare const GiIceland: IconType;
export declare const GiIciclesAura: IconType;
export declare const GiIciclesFence: IconType;
export declare const GiIdCard: IconType;
export declare const GiIdea: IconType;
export declare const GiIfrit: IconType;
export declare const GiIgloo: IconType;
export declare const GiImbricatedArrows: IconType;
export declare const GiImpLaugh: IconType;
export declare const GiImp: IconType;
export declare const GiImpactPoint: IconType;
export declare const GiImperialCrown: IconType;
export declare const GiImplosion: IconType;
export declare const GiImprisoned: IconType;
export declare const GiInauguration: IconType;
export declare const GiIncense: IconType;
export declare const GiIncisors: IconType;
export declare const GiIncomingRocket: IconType;
export declare const GiIncubator: IconType;
export declare const GiIndiaGate: IconType;
export declare const GiIndianPalace: IconType;
export declare const GiInfernoBomb: IconType;
export declare const GiInfestedMass: IconType;
export declare const GiInfinity: IconType;
export declare const GiInfo: IconType;
export declare const GiInjustice: IconType;
export declare const GiInkSwirl: IconType;
export declare const GiInnerSelf: IconType;
export declare const GiInsectJaws: IconType;
export declare const GiInspiration: IconType;
export declare const GiInterceptorShip: IconType;
export declare const GiInterdiction: IconType;
export declare const GiInterlacedTentacles: IconType;
export declare const GiInterleavedArrows: IconType;
export declare const GiInterleavedClaws: IconType;
export declare const GiInternalInjury: IconType;
export declare const GiInternalOrgan: IconType;
export declare const GiInterstellarPath: IconType;
export declare const GiIntricateNecklace: IconType;
export declare const GiInvertedDice1: IconType;
export declare const GiInvertedDice2: IconType;
export declare const GiInvertedDice3: IconType;
export declare const GiInvertedDice4: IconType;
export declare const GiInvertedDice5: IconType;
export declare const GiInvertedDice6: IconType;
export declare const GiInvisibleFace: IconType;
export declare const GiInvisible: IconType;
export declare const GiIonCannonBlast: IconType;
export declare const GiIonicColumn: IconType;
export declare const GiIraq: IconType;
export declare const GiIronCross: IconType;
export declare const GiIronHulledWarship: IconType;
export declare const GiIronMask: IconType;
export declare const GiIsland: IconType;
export declare const GiItalia: IconType;
export declare const GiIvoryTusks: IconType;
export declare const GiJBrick: IconType;
export declare const GiJackPlug: IconType;
export declare const GiJamesBondAperture: IconType;
export declare const GiJapan: IconType;
export declare const GiJapaneseBridge: IconType;
export declare const GiJasmine: IconType;
export declare const GiJasonMask: IconType;
export declare const GiJawbone: IconType;
export declare const GiJawlessCyclop: IconType;
export declare const GiJeep: IconType;
export declare const GiJellyBeans: IconType;
export declare const GiJelly: IconType;
export declare const GiJellyfish: IconType;
export declare const GiJerrycan: IconType;
export declare const GiJerusalemCross: IconType;
export declare const GiJesterHat: IconType;
export declare const GiJetFighter: IconType;
export declare const GiJetPack: IconType;
export declare const GiJetpack: IconType;
export declare const GiJewelCrown: IconType;
export declare const GiJeweledChalice: IconType;
export declare const GiJigsawBox: IconType;
export declare const GiJigsawPiece: IconType;
export declare const GiJoin: IconType;
export declare const GiJoint: IconType;
export declare const GiJourney: IconType;
export declare const GiJoystick: IconType;
export declare const GiJug: IconType;
export declare const GiJuggler: IconType;
export declare const GiJugglingClubs: IconType;
export declare const GiJugglingSeal: IconType;
export declare const GiJumpAcross: IconType;
export declare const GiJumpingDog: IconType;
export declare const GiJumpingRope: IconType;
export declare const GiJungle: IconType;
export declare const GiJupiter: IconType;
export declare const GiJusticeStar: IconType;
export declare const GiKaleidoscopePearls: IconType;
export declare const GiKangaroo: IconType;
export declare const GiKatana: IconType;
export declare const GiKebabSpit: IconType;
export declare const GiKenkuHead: IconType;
export declare const GiKenya: IconType;
export declare const GiKetchup: IconType;
export declare const GiKevlarVest: IconType;
export declare const GiKevlar: IconType;
export declare const GiKeyCard: IconType;
export declare const GiKeyLock: IconType;
export declare const GiKey: IconType;
export declare const GiKeyboard: IconType;
export declare const GiKeyring: IconType;
export declare const GiKickScooter: IconType;
export declare const GiKidSlide: IconType;
export declare const GiKidneys: IconType;
export declare const GiKimono: IconType;
export declare const GiKindle: IconType;
export declare const GiKingJuMask: IconType;
export declare const GiKing: IconType;
export declare const GiKitchenKnives: IconType;
export declare const GiKitchenScale: IconType;
export declare const GiKitchenTap: IconType;
export declare const GiKite: IconType;
export declare const GiKiwiBird: IconType;
export declare const GiKiwiFruit: IconType;
export declare const GiKlingon: IconType;
export declare const GiKnapsack: IconType;
export declare const GiKneeBandage: IconType;
export declare const GiKneeCap: IconType;
export declare const GiKneePad: IconType;
export declare const GiKneeling: IconType;
export declare const GiKnifeFork: IconType;
export declare const GiKnifeThrust: IconType;
export declare const GiKnightBanner: IconType;
export declare const GiKnockedOutStars: IconType;
export declare const GiKnockout: IconType;
export declare const GiKnot: IconType;
export declare const GiKoala: IconType;
export declare const GiKoholintEgg: IconType;
export declare const GiKrakenTentacle: IconType;
export declare const GiKusarigama: IconType;
export declare const GiLBrick: IconType;
export declare const GiLabCoat: IconType;
export declare const GiLabradorHead: IconType;
export declare const GiLadder: IconType;
export declare const GiLaddersPlatform: IconType;
export declare const GiLadle: IconType;
export declare const GiLadybug: IconType;
export declare const GiLamellar: IconType;
export declare const GiLampreyMouth: IconType;
export declare const GiLandMine: IconType;
export declare const GiLanternFlame: IconType;
export declare const GiLantern: IconType;
export declare const GiLaptop: IconType;
export declare const GiLargeDress: IconType;
export declare const GiLargePaintBrush: IconType;
export declare const GiLargeWound: IconType;
export declare const GiLaserBlast: IconType;
export declare const GiLaserBurst: IconType;
export declare const GiLaserGun: IconType;
export declare const GiLaserPrecision: IconType;
export declare const GiLaserSparks: IconType;
export declare const GiLaserTurret: IconType;
export declare const GiLaserWarning: IconType;
export declare const GiLaserburn: IconType;
export declare const GiLasso: IconType;
export declare const GiLatvia: IconType;
export declare const GiLaurelCrown: IconType;
export declare const GiLaurelsTrophy: IconType;
export declare const GiLaurels: IconType;
export declare const GiLava: IconType;
export declare const GiLawStar: IconType;
export declare const GiLayeredArmor: IconType;
export declare const GiLeadPipe: IconType;
export declare const GiLeafSkeleton: IconType;
export declare const GiLeafSwirl: IconType;
export declare const GiLeak: IconType;
export declare const GiLeakySkull: IconType;
export declare const GiLeapfrog: IconType;
export declare const GiLeatherArmor: IconType;
export declare const GiLeatherBoot: IconType;
export declare const GiLeatherVest: IconType;
export declare const GiLed: IconType;
export declare const GiLeeEnfield: IconType;
export declare const GiLeechingWorm: IconType;
export declare const GiLeek: IconType;
export declare const GiLegArmor: IconType;
export declare const GiLeg: IconType;
export declare const GiLemon: IconType;
export declare const GiLeo: IconType;
export declare const GiLetterBomb: IconType;
export declare const GiLevelCrossing: IconType;
export declare const GiLevelEndFlag: IconType;
export declare const GiLevelFourAdvanced: IconType;
export declare const GiLevelFour: IconType;
export declare const GiLevelThreeAdvanced: IconType;
export declare const GiLevelThree: IconType;
export declare const GiLevelTwoAdvanced: IconType;
export declare const GiLevelTwo: IconType;
export declare const GiLever: IconType;
export declare const GiLiar: IconType;
export declare const GiLibertyWing: IconType;
export declare const GiLibra: IconType;
export declare const GiLibya: IconType;
export declare const GiLifeBar: IconType;
export declare const GiLifeBuoy: IconType;
export declare const GiLifeInTheBalance: IconType;
export declare const GiLifeJacket: IconType;
export declare const GiLifeSupport: IconType;
export declare const GiLifeTap: IconType;
export declare const GiLift: IconType;
export declare const GiLightBackpack: IconType;
export declare const GiLightBulb: IconType;
export declare const GiLightFighter: IconType;
export declare const GiLightHelm: IconType;
export declare const GiLightProjector: IconType;
export declare const GiLightSabers: IconType;
export declare const GiLightThornyTriskelion: IconType;
export declare const GiLighter: IconType;
export declare const GiLighthouse: IconType;
export declare const GiLightningArc: IconType;
export declare const GiLightningBow: IconType;
export declare const GiLightningBranches: IconType;
export declare const GiLightningDissipation: IconType;
export declare const GiLightningDome: IconType;
export declare const GiLightningElectron: IconType;
export declare const GiLightningFlame: IconType;
export declare const GiLightningFrequency: IconType;
export declare const GiLightningHelix: IconType;
export declare const GiLightningMask: IconType;
export declare const GiLightningSaber: IconType;
export declare const GiLightningShadow: IconType;
export declare const GiLightningShield: IconType;
export declare const GiLightningShout: IconType;
export declare const GiLightningSlashes: IconType;
export declare const GiLightningSpanner: IconType;
export declare const GiLightningStorm: IconType;
export declare const GiLightningTear: IconType;
export declare const GiLightningTree: IconType;
export declare const GiLightningTrio: IconType;
export declare const GiLilyPads: IconType;
export declare const GiLindenLeaf: IconType;
export declare const GiLinkedRings: IconType;
export declare const GiLion: IconType;
export declare const GiLips: IconType;
export declare const GiLipstick: IconType;
export declare const GiLiquidSoap: IconType;
export declare const GiLitCandelabra: IconType;
export declare const GiLiver: IconType;
export declare const GiLizardTongue: IconType;
export declare const GiLizardman: IconType;
export declare const GiLoad: IconType;
export declare const GiLobArrow: IconType;
export declare const GiLockPicking: IconType;
export declare const GiLockSpy: IconType;
export declare const GiLockedBox: IconType;
export declare const GiLockedChest: IconType;
export declare const GiLockedDoor: IconType;
export declare const GiLockedFortress: IconType;
export declare const GiLockedHeart: IconType;
export declare const GiLockers: IconType;
export declare const GiLockpicks: IconType;
export declare const GiLog: IconType;
export declare const GiLogging: IconType;
export declare const GiLogicGateAnd: IconType;
export declare const GiLogicGateNand: IconType;
export declare const GiLogicGateNor: IconType;
export declare const GiLogicGateNot: IconType;
export declare const GiLogicGateNxor: IconType;
export declare const GiLogicGateOr: IconType;
export declare const GiLogicGateXor: IconType;
export declare const GiLoincloth: IconType;
export declare const GiLongAntennaeBug: IconType;
export declare const GiLongLeggedSpider: IconType;
export declare const GiLookAt: IconType;
export declare const GiLorgnette: IconType;
export declare const GiLostLimb: IconType;
export declare const GiLotusFlower: IconType;
export declare const GiLotus: IconType;
export declare const GiLouvrePyramid: IconType;
export declare const GiLoveHowl: IconType;
export declare const GiLoveInjection: IconType;
export declare const GiLoveLetter: IconType;
export declare const GiLoveMystery: IconType;
export declare const GiLoveSong: IconType;
export declare const GiLovers: IconType;
export declare const GiLowTide: IconType;
export declare const GiLuchador: IconType;
export declare const GiLuciferCannon: IconType;
export declare const GiLuckyFisherman: IconType;
export declare const GiLuger: IconType;
export declare const GiLunarModule: IconType;
export declare const GiLunarWand: IconType;
export declare const GiLungs: IconType;
export declare const GiLynxHead: IconType;
export declare const GiLyre: IconType;
export declare const GiM3GreaseGun: IconType;
export declare const GiMac10: IconType;
export declare const GiMaceHead: IconType;
export declare const GiMachete: IconType;
export declare const GiMachineGunMagazine: IconType;
export declare const GiMachineGun: IconType;
export declare const GiMadScientist: IconType;
export declare const GiMaggot: IconType;
export declare const GiMagicAxe: IconType;
export declare const GiMagicBroom: IconType;
export declare const GiMagicGate: IconType;
export declare const GiMagicHat: IconType;
export declare const GiMagicLamp: IconType;
export declare const GiMagicPalm: IconType;
export declare const GiMagicPortal: IconType;
export declare const GiMagicPotion: IconType;
export declare const GiMagicShield: IconType;
export declare const GiMagicSwirl: IconType;
export declare const GiMagicTrident: IconType;
export declare const GiMagickTrick: IconType;
export declare const GiMagnetBlast: IconType;
export declare const GiMagnetMan: IconType;
export declare const GiMagnet: IconType;
export declare const GiMagnifyingGlass: IconType;
export declare const GiMailShirt: IconType;
export declare const GiMailbox: IconType;
export declare const GiMailedFist: IconType;
export declare const GiMale: IconType;
export declare const GiMammoth: IconType;
export declare const GiManacles: IconType;
export declare const GiMandrillHead: IconType;
export declare const GiMantaRay: IconType;
export declare const GiMantrap: IconType;
export declare const GiManualJuicer: IconType;
export declare const GiManualMeatGrinder: IconType;
export declare const GiMapleLeaf: IconType;
export declare const GiMaracas: IconType;
export declare const GiMarbleTap: IconType;
export declare const GiMarbles: IconType;
export declare const GiMarrowDrain: IconType;
export declare const GiMarsCuriosity: IconType;
export declare const GiMarsPathfinder: IconType;
export declare const GiMarshmallows: IconType;
export declare const GiMartini: IconType;
export declare const GiMartyrMemorial: IconType;
export declare const GiMaskedSpider: IconType;
export declare const GiMasonJar: IconType;
export declare const GiMassDriver: IconType;
export declare const GiMasterOfArms: IconType;
export declare const GiMatchHead: IconType;
export declare const GiMatchTip: IconType;
export declare const GiMatchbox: IconType;
export declare const GiMaterialsScience: IconType;
export declare const GiMatryoshkaDolls: IconType;
export declare const GiMatterStates: IconType;
export declare const GiMayanPyramid: IconType;
export declare const GiMazeCornea: IconType;
export declare const GiMazeSaw: IconType;
export declare const GiMaze: IconType;
export declare const GiMeal: IconType;
export declare const GiMeatCleaver: IconType;
export declare const GiMeatHook: IconType;
export declare const GiMeat: IconType;
export declare const GiMechaHead: IconType;
export declare const GiMechaMask: IconType;
export declare const GiMechanicGarage: IconType;
export declare const GiMechanicalArm: IconType;
export declare const GiMedalSkull: IconType;
export declare const GiMedal: IconType;
export declare const GiMedallist: IconType;
export declare const GiMedicalDrip: IconType;
export declare const GiMedicalPackAlt: IconType;
export declare const GiMedicalPack: IconType;
export declare const GiMedicalThermometer: IconType;
export declare const GiMedicinePills: IconType;
export declare const GiMedicines: IconType;
export declare const GiMedievalBarracks: IconType;
export declare const GiMedievalGate: IconType;
export declare const GiMedievalPavilion: IconType;
export declare const GiMeditation: IconType;
export declare const GiMedusaHead: IconType;
export declare const GiMeepleArmy: IconType;
export declare const GiMeepleCircle: IconType;
export declare const GiMeepleGroup: IconType;
export declare const GiMeepleKing: IconType;
export declare const GiMeeple: IconType;
export declare const GiMegabot: IconType;
export declare const GiMegaphone: IconType;
export declare const GiMeltingIceCube: IconType;
export declare const GiMeltingMetal: IconType;
export declare const GiMenhir: IconType;
export declare const GiMermaid: IconType;
export declare const GiMeshBall: IconType;
export declare const GiMeshNetwork: IconType;
export declare const GiMetalBar: IconType;
export declare const GiMetalBoot: IconType;
export declare const GiMetalDisc: IconType;
export declare const GiMetalGolemHead: IconType;
export declare const GiMetalHand: IconType;
export declare const GiMetalPlate: IconType;
export declare const GiMetalScales: IconType;
export declare const GiMetalSkirt: IconType;
export declare const GiMeteorImpact: IconType;
export declare const GiMetroid: IconType;
export declare const GiMetronome: IconType;
export declare const GiMexico: IconType;
export declare const GiMicrochip: IconType;
export declare const GiMicrophone: IconType;
export declare const GiMicroscopeLens: IconType;
export declare const GiMicroscope: IconType;
export declare const GiMiddleArrow: IconType;
export declare const GiMidnightClaw: IconType;
export declare const GiMightyBoosh: IconType;
export declare const GiMightyForce: IconType;
export declare const GiMightyHorn: IconType;
export declare const GiMightySpanner: IconType;
export declare const GiMilitaryAmbulance: IconType;
export declare const GiMilitaryFort: IconType;
export declare const GiMilkCarton: IconType;
export declare const GiMilleniumKey: IconType;
export declare const GiMimicChest: IconType;
export declare const GiMineExplosion: IconType;
export declare const GiMineTruck: IconType;
export declare const GiMineWagon: IconType;
export declare const GiMinefield: IconType;
export declare const GiMiner: IconType;
export declare const GiMineralHeart: IconType;
export declare const GiMineralPearls: IconType;
export declare const GiMinerals: IconType;
export declare const GiMiniSubmarine: IconType;
export declare const GiMinigun: IconType;
export declare const GiMiningHelmet: IconType;
export declare const GiMining: IconType;
export declare const GiMinions: IconType;
export declare const GiMinotaur: IconType;
export declare const GiMiracleMedecine: IconType;
export declare const GiMirrorMirror: IconType;
export declare const GiMisdirection: IconType;
export declare const GiMissileLauncher: IconType;
export declare const GiMissileMech: IconType;
export declare const GiMissilePod: IconType;
export declare const GiMissileSwarm: IconType;
export declare const GiMiteAlt: IconType;
export declare const GiMite: IconType;
export declare const GiMoai: IconType;
export declare const GiModernCity: IconType;
export declare const GiMoebiusStar: IconType;
export declare const GiMoebiusTrefoil: IconType;
export declare const GiMoebiusTriangle: IconType;
export declare const GiMokaPot: IconType;
export declare const GiMoldova: IconType;
export declare const GiMolecule: IconType;
export declare const GiMolotov: IconType;
export declare const GiMonaLisa: IconType;
export declare const GiMonclerJacket: IconType;
export declare const GiMoneyStack: IconType;
export declare const GiMongolia: IconType;
export declare const GiMonkFace: IconType;
export declare const GiMonkeyWrench: IconType;
export declare const GiMonkey: IconType;
export declare const GiMonoWheelRobot: IconType;
export declare const GiMonsterGrasp: IconType;
export declare const GiMonsteraLeaf: IconType;
export declare const GiMonumentValley: IconType;
export declare const GiMoonBats: IconType;
export declare const GiMoonClaws: IconType;
export declare const GiMoonOrbit: IconType;
export declare const GiMoon: IconType;
export declare const GiMooringBollard: IconType;
export declare const GiMorbidHumour: IconType;
export declare const GiMorgueFeet: IconType;
export declare const GiMorphBall: IconType;
export declare const GiMortar: IconType;
export declare const GiMountainCave: IconType;
export declare const GiMountainClimbing: IconType;
export declare const GiMountainRoad: IconType;
export declare const GiMountains: IconType;
export declare const GiMountaintop: IconType;
export declare const GiMountedKnight: IconType;
export declare const GiMouse: IconType;
export declare const GiMouthWatering: IconType;
export declare const GiMove: IconType;
export declare const GiMovementSensor: IconType;
export declare const GiMp40: IconType;
export declare const GiMp5: IconType;
export declare const GiMp5K: IconType;
export declare const GiMucousPillar: IconType;
export declare const GiMugShot: IconType;
export declare const GiMultiDirections: IconType;
export declare const GiMultipleTargets: IconType;
export declare const GiMummyHead: IconType;
export declare const GiMuscleFat: IconType;
export declare const GiMuscleUp: IconType;
export declare const GiMuscularTorso: IconType;
export declare const GiMushroomCloud: IconType;
export declare const GiMushroomGills: IconType;
export declare const GiMushroomHouse: IconType;
export declare const GiMushroom: IconType;
export declare const GiMushroomsCluster: IconType;
export declare const GiMushrooms: IconType;
export declare const GiMusicSpell: IconType;
export declare const GiMusicalKeyboard: IconType;
export declare const GiMusicalNotes: IconType;
export declare const GiMusicalScore: IconType;
export declare const GiMusket: IconType;
export declare const GiMussel: IconType;
export declare const GiMustache: IconType;
export declare const GiMute: IconType;
export declare const GiNachos: IconType;
export declare const GiNailedFoot: IconType;
export declare const GiNailedHead: IconType;
export declare const GiNails: IconType;
export declare const GiNanoBot: IconType;
export declare const GiNautilusShell: IconType;
export declare const GiNeckBite: IconType;
export declare const GiNecklaceDisplay: IconType;
export declare const GiNecklace: IconType;
export declare const GiNeedleDrill: IconType;
export declare const GiNeedleJaws: IconType;
export declare const GiNefertiti: IconType;
export declare const GiNestBirds: IconType;
export declare const GiNestEggs: IconType;
export declare const GiNestedEclipses: IconType;
export declare const GiNestedHearts: IconType;
export declare const GiNestedHexagons: IconType;
export declare const GiNetworkBars: IconType;
export declare const GiNewBorn: IconType;
export declare const GiNewShoot: IconType;
export declare const GiNewspaper: IconType;
export declare const GiNextButton: IconType;
export declare const GiNigeria: IconType;
export declare const GiNightSky: IconType;
export declare const GiNightSleep: IconType;
export declare const GiNightVision: IconType;
export declare const GiNinjaArmor: IconType;
export declare const GiNinjaHead: IconType;
export declare const GiNinjaHeroicStance: IconType;
export declare const GiNinjaMask: IconType;
export declare const GiNinjaStar: IconType;
export declare const GiNinjaVelociraptor: IconType;
export declare const GiNodular: IconType;
export declare const GiNoodleBall: IconType;
export declare const GiNoodles: IconType;
export declare const GiNorthStarShuriken: IconType;
export declare const GiNoseFront: IconType;
export declare const GiNoseSide: IconType;
export declare const GiNotebook: IconType;
export declare const GiNothingToSay: IconType;
export declare const GiNuclearBomb: IconType;
export declare const GiNuclearPlant: IconType;
export declare const GiNuclearWaste: IconType;
export declare const GiNuclear: IconType;
export declare const GiNunFace: IconType;
export declare const GiNunchaku: IconType;
export declare const GiNurseFemale: IconType;
export declare const GiNurseMale: IconType;
export declare const GiOBrick: IconType;
export declare const GiOakLeaf: IconType;
export declare const GiOak: IconType;
export declare const GiOasis: IconType;
export declare const GiOat: IconType;
export declare const GiObelisk: IconType;
export declare const GiObservatory: IconType;
export declare const GiOcarina: IconType;
export declare const GiOccupy: IconType;
export declare const GiOctogonalEye: IconType;
export declare const GiOctoman: IconType;
export declare const GiOctopus: IconType;
export declare const GiOden: IconType;
export declare const GiOfficeChair: IconType;
export declare const GiOffshorePlatform: IconType;
export declare const GiOgre: IconType;
export declare const GiOilDrum: IconType;
export declare const GiOilPump: IconType;
export declare const GiOilRig: IconType;
export declare const GiOilySpiral: IconType;
export declare const GiOldKing: IconType;
export declare const GiOldLantern: IconType;
export declare const GiOldMicrophone: IconType;
export declare const GiOldWagon: IconType;
export declare const GiOlive: IconType;
export declare const GiOmega: IconType;
export declare const GiOnSight: IconType;
export declare const GiOnTarget: IconType;
export declare const GiOneEyed: IconType;
export declare const GiOni: IconType;
export declare const GiOnigori: IconType;
export declare const GiOpenBook: IconType;
export declare const GiOpenChest: IconType;
export declare const GiOpenFolder: IconType;
export declare const GiOpenGate: IconType;
export declare const GiOpenPalm: IconType;
export declare const GiOpenTreasureChest: IconType;
export declare const GiOpenWound: IconType;
export declare const GiOpenedFoodCan: IconType;
export declare const GiOpeningShell: IconType;
export declare const GiOphiuchus: IconType;
export declare const GiOppidum: IconType;
export declare const GiOppositeHearts: IconType;
export declare const GiOppression: IconType;
export declare const GiOrangeSlice: IconType;
export declare const GiOrange: IconType;
export declare const GiOrbDirection: IconType;
export declare const GiOrbWand: IconType;
export declare const GiOrbit: IconType;
export declare const GiOrbitalRays: IconType;
export declare const GiOrbital: IconType;
export declare const GiOrcHead: IconType;
export declare const GiOre: IconType;
export declare const GiOrganigram: IconType;
export declare const GiOstrich: IconType;
export declare const GiOuroboros: IconType;
export declare const GiOutbackHat: IconType;
export declare const GiOverInfinity: IconType;
export declare const GiOverdose: IconType;
export declare const GiOverdrive: IconType;
export declare const GiOverhead: IconType;
export declare const GiOverkill: IconType;
export declare const GiOverlordHelm: IconType;
export declare const GiOvermind: IconType;
export declare const GiOwl: IconType;
export declare const GiOysterPearl: IconType;
export declare const GiOyster: IconType;
export declare const GiP90: IconType;
export declare const GiPackedPlanks: IconType;
export declare const GiPaddleSteamer: IconType;
export declare const GiPaddles: IconType;
export declare const GiPadlockOpen: IconType;
export declare const GiPadlock: IconType;
export declare const GiPagoda: IconType;
export declare const GiPaintBrush: IconType;
export declare const GiPaintBucket: IconType;
export declare const GiPaintRoller: IconType;
export declare const GiPaintedPottery: IconType;
export declare const GiPalette: IconType;
export declare const GiPalisade: IconType;
export declare const GiPalmTree: IconType;
export declare const GiPalm: IconType;
export declare const GiPanFlute: IconType;
export declare const GiPanda: IconType;
export declare const GiPangolin: IconType;
export declare const GiPanzerfaust: IconType;
export declare const GiPaperArrow: IconType;
export declare const GiPaperBagCrumpled: IconType;
export declare const GiPaperBagFolded: IconType;
export declare const GiPaperBagOpen: IconType;
export declare const GiPaperBoat: IconType;
export declare const GiPaperBomb: IconType;
export declare const GiPaperClip: IconType;
export declare const GiPaperCrane: IconType;
export declare const GiPaperFrog: IconType;
export declare const GiPaperLantern: IconType;
export declare const GiPaperPlane: IconType;
export declare const GiPaperTray: IconType;
export declare const GiPaperWindmill: IconType;
export declare const GiPaper: IconType;
export declare const GiPapers: IconType;
export declare const GiPapyrus: IconType;
export declare const GiParachute: IconType;
export declare const GiParaguay: IconType;
export declare const GiParanoia: IconType;
export declare const GiParasaurolophus: IconType;
export declare const GiParkBench: IconType;
export declare const GiParmecia: IconType;
export declare const GiParrotHead: IconType;
export declare const GiPartyFlags: IconType;
export declare const GiPartyHat: IconType;
export declare const GiPartyPopper: IconType;
export declare const GiPassport: IconType;
export declare const GiPathDistance: IconType;
export declare const GiPathTile: IconType;
export declare const GiPauldrons: IconType;
export declare const GiPauseButton: IconType;
export declare const GiPawFront: IconType;
export declare const GiPawHeart: IconType;
export declare const GiPawPrint: IconType;
export declare const GiPaw: IconType;
export declare const GiPawn: IconType;
export declare const GiPayMoney: IconType;
export declare const GiPc: IconType;
export declare const GiPeaceDove: IconType;
export declare const GiPeach: IconType;
export declare const GiPeaks: IconType;
export declare const GiPeanut: IconType;
export declare const GiPear: IconType;
export declare const GiPearlEarring: IconType;
export declare const GiPearlNecklace: IconType;
export declare const GiPeas: IconType;
export declare const GiPegasus: IconType;
export declare const GiPelvisBone: IconType;
export declare const GiPencilBrush: IconType;
export declare const GiPencilRuler: IconType;
export declare const GiPencil: IconType;
export declare const GiPendantKey: IconType;
export declare const GiPendulumSwing: IconType;
export declare const GiPenguin: IconType;
export declare const GiPentacle: IconType;
export declare const GiPentagramRose: IconType;
export declare const GiPentarrowsTornado: IconType;
export declare const GiPerfumeBottle: IconType;
export declare const GiPeriscope: IconType;
export declare const GiPerpendicularRings: IconType;
export declare const GiPersonInBed: IconType;
export declare const GiPerson: IconType;
export declare const GiPerspectiveDiceFive: IconType;
export declare const GiPerspectiveDiceFour: IconType;
export declare const GiPerspectiveDiceOne: IconType;
export declare const GiPerspectiveDiceSixFacesFive: IconType;
export declare const GiPerspectiveDiceSixFacesFour: IconType;
export declare const GiPerspectiveDiceSixFacesOne: IconType;
export declare const GiPerspectiveDiceSixFacesRandom: IconType;
export declare const GiPerspectiveDiceSixFacesSix: IconType;
export declare const GiPerspectiveDiceSixFacesThree: IconType;
export declare const GiPerspectiveDiceSixFacesTwo: IconType;
export declare const GiPerspectiveDiceSix: IconType;
export declare const GiPerspectiveDiceThree: IconType;
export declare const GiPerspectiveDiceTwo: IconType;
export declare const GiPeru: IconType;
export declare const GiPestleMortar: IconType;
export declare const GiPharoah: IconType;
export declare const GiPhone: IconType;
export declare const GiPhotoCamera: IconType;
export declare const GiPhrygianCap: IconType;
export declare const GiPianist: IconType;
export declare const GiPianoKeys: IconType;
export declare const GiPickOfDestiny: IconType;
export declare const GiPickelhaube: IconType;
export declare const GiPickle: IconType;
export declare const GiPieChart: IconType;
export declare const GiPieSlice: IconType;
export declare const GiPieceSkull: IconType;
export declare const GiPiercedBody: IconType;
export declare const GiPiercedHeart: IconType;
export declare const GiPiercingSword: IconType;
export declare const GiPigFace: IconType;
export declare const GiPig: IconType;
export declare const GiPiggyBank: IconType;
export declare const GiPikeman: IconType;
export declare const GiPilgrimHat: IconType;
export declare const GiPillDrop: IconType;
export declare const GiPill: IconType;
export declare const GiPillow: IconType;
export declare const GiPimiento: IconType;
export declare const GiPin: IconType;
export declare const GiPinata: IconType;
export declare const GiPinballFlipper: IconType;
export declare const GiPincers: IconType;
export declare const GiPineTree: IconType;
export declare const GiPineapple: IconType;
export declare const GiPingPongBat: IconType;
export declare const GiPipeOrgan: IconType;
export declare const GiPipes: IconType;
export declare const GiPiranha: IconType;
export declare const GiPirateCannon: IconType;
export declare const GiPirateCaptain: IconType;
export declare const GiPirateCoat: IconType;
export declare const GiPirateFlag: IconType;
export declare const GiPirateGrave: IconType;
export declare const GiPirateHat: IconType;
export declare const GiPirateHook: IconType;
export declare const GiPirateSkull: IconType;
export declare const GiPisaTower: IconType;
export declare const GiPisces: IconType;
export declare const GiPistolGun: IconType;
export declare const GiPitchfork: IconType;
export declare const GiPizzaCutter: IconType;
export declare const GiPizzaSlice: IconType;
export declare const GiPlagueDoctorProfile: IconType;
export declare const GiPlainArrow: IconType;
export declare const GiPlainCircle: IconType;
export declare const GiPlainDagger: IconType;
export declare const GiPlainSquare: IconType;
export declare const GiPlanePilot: IconType;
export declare const GiPlaneWing: IconType;
export declare const GiPlanetConquest: IconType;
export declare const GiPlanetCore: IconType;
export declare const GiPlanks: IconType;
export declare const GiPlantRoots: IconType;
export declare const GiPlantSeed: IconType;
export declare const GiPlantWatering: IconType;
export declare const GiPlantsAndAnimals: IconType;
export declare const GiPlasmaBolt: IconType;
export declare const GiPlasticDuck: IconType;
export declare const GiPlastron: IconType;
export declare const GiPlateClaw: IconType;
export declare const GiPlatform: IconType;
export declare const GiPlayButton: IconType;
export declare const GiPlayerBase: IconType;
export declare const GiPlayerNext: IconType;
export declare const GiPlayerPrevious: IconType;
export declare const GiPlayerTime: IconType;
export declare const GiPlesiosaurus: IconType;
export declare const GiPlow: IconType;
export declare const GiPlug: IconType;
export declare const GiPlunger: IconType;
export declare const GiPocketBow: IconType;
export declare const GiPocketRadio: IconType;
export declare const GiPocketWatch: IconType;
export declare const GiPodiumSecond: IconType;
export declare const GiPodiumThird: IconType;
export declare const GiPodiumWinner: IconType;
export declare const GiPodium: IconType;
export declare const GiPointing: IconType;
export declare const GiPointyHat: IconType;
export declare const GiPointySword: IconType;
export declare const GiPoisonBottle: IconType;
export declare const GiPoisonCloud: IconType;
export declare const GiPoisonGas: IconType;
export declare const GiPoison: IconType;
export declare const GiPokecog: IconType;
export declare const GiPokerHand: IconType;
export declare const GiPoland: IconType;
export declare const GiPolarBear: IconType;
export declare const GiPolarStar: IconType;
export declare const GiPoliceBadge: IconType;
export declare const GiPoliceCar: IconType;
export declare const GiPoliceOfficerHead: IconType;
export declare const GiPoliceTarget: IconType;
export declare const GiPollenDust: IconType;
export declare const GiPoloShirt: IconType;
export declare const GiPoncho: IconType;
export declare const GiPoolDive: IconType;
export declare const GiPoolTableCorner: IconType;
export declare const GiPoolTriangle: IconType;
export declare const GiPopcorn: IconType;
export declare const GiPopeCrown: IconType;
export declare const GiPoppy: IconType;
export declare const GiPorcelainVase: IconType;
export declare const GiPorcupine: IconType;
export declare const GiPorcupinefish: IconType;
export declare const GiPortal: IconType;
export declare const GiPortculis: IconType;
export declare const GiPortrait: IconType;
export declare const GiPortugal: IconType;
export declare const GiPositionMarker: IconType;
export declare const GiPostOffice: IconType;
export declare const GiPostStamp: IconType;
export declare const GiPotato: IconType;
export declare const GiPotionBall: IconType;
export declare const GiPotionOfMadness: IconType;
export declare const GiPounce: IconType;
export declare const GiPouringChalice: IconType;
export declare const GiPouringPot: IconType;
export declare const GiPowderBag: IconType;
export declare const GiPowder: IconType;
export declare const GiPowerButton: IconType;
export declare const GiPowerGenerator: IconType;
export declare const GiPowerLightning: IconType;
export declare const GiPowerRing: IconType;
export declare const GiPrayerBeads: IconType;
export declare const GiPrayer: IconType;
export declare const GiPrayingMantis: IconType;
export declare const GiPresent: IconType;
export declare const GiPressureCooker: IconType;
export declare const GiPrettyFangs: IconType;
export declare const GiPretzel: IconType;
export declare const GiPreviousButton: IconType;
export declare const GiPriceTag: IconType;
export declare const GiPrimitiveNecklace: IconType;
export declare const GiPrimitiveTorch: IconType;
export declare const GiPrism: IconType;
export declare const GiPrisoner: IconType;
export declare const GiPrivateFirstClass: IconType;
export declare const GiPrivate: IconType;
export declare const GiProcessor: IconType;
export declare const GiProfit: IconType;
export declare const GiProgression: IconType;
export declare const GiPropellerBeanie: IconType;
export declare const GiProtectionGlasses: IconType;
export declare const GiPschentDoubleCrown: IconType;
export declare const GiPsychicWaves: IconType;
export declare const GiPterodactylus: IconType;
export declare const GiPteruges: IconType;
export declare const GiPublicSpeaker: IconType;
export declare const GiPull: IconType;
export declare const GiPulleyHook: IconType;
export declare const GiPulse: IconType;
export declare const GiPummeled: IconType;
export declare const GiPumpkinLantern: IconType;
export declare const GiPumpkinMask: IconType;
export declare const GiPumpkin: IconType;
export declare const GiPunchBlast: IconType;
export declare const GiPunch: IconType;
export declare const GiPunchingBag: IconType;
export declare const GiPuppet: IconType;
export declare const GiPurpleTentacle: IconType;
export declare const GiPush: IconType;
export declare const GiPuzzle: IconType;
export declare const GiPylon: IconType;
export declare const GiPyre: IconType;
export declare const GiPyromaniac: IconType;
export declare const GiQaitbayCitadel: IconType;
export declare const GiQuakeStomp: IconType;
export declare const GiQueenCrown: IconType;
export declare const GiQuickMan: IconType;
export declare const GiQuickSlash: IconType;
export declare const GiQuicksand: IconType;
export declare const GiQuillInk: IconType;
export declare const GiQuill: IconType;
export declare const GiQuiver: IconType;
export declare const GiRabbitHead: IconType;
export declare const GiRabbit: IconType;
export declare const GiRaccoonHead: IconType;
export declare const GiRaceCar: IconType;
export declare const GiRadarCrossSection: IconType;
export declare const GiRadarDish: IconType;
export declare const GiRadarSweep: IconType;
export declare const GiRaddish: IconType;
export declare const GiRadialBalance: IconType;
export declare const GiRadiations: IconType;
export declare const GiRadioTower: IconType;
export declare const GiRadioactive: IconType;
export declare const GiRaft: IconType;
export declare const GiRaggedWound: IconType;
export declare const GiRailRoad: IconType;
export declare const GiRailway: IconType;
export declare const GiRainbowStar: IconType;
export declare const GiRaining: IconType;
export declare const GiRaiseSkeleton: IconType;
export declare const GiRaiseZombie: IconType;
export declare const GiRake: IconType;
export declare const GiRallyTheTroops: IconType;
export declare const GiRamProfile: IconType;
export declare const GiRam: IconType;
export declare const GiRanchGate: IconType;
export declare const GiRank1: IconType;
export declare const GiRank2: IconType;
export declare const GiRank3: IconType;
export declare const GiRapidshareArrow: IconType;
export declare const GiRaspberry: IconType;
export declare const GiRat: IconType;
export declare const GiRattlesnake: IconType;
export declare const GiRaven: IconType;
export declare const GiRawEgg: IconType;
export declare const GiRayGun: IconType;
export declare const GiRazorBlade: IconType;
export declare const GiRazor: IconType;
export declare const GiReactor: IconType;
export declare const GiRead: IconType;
export declare const GiReaperScythe: IconType;
export declare const GiRearAura: IconType;
export declare const GiReceiveMoney: IconType;
export declare const GiRecycle: IconType;
export declare const GiRedCarpet: IconType;
export declare const GiReed: IconType;
export declare const GiRefinery: IconType;
export declare const GiRegeneration: IconType;
export declare const GiRelationshipBounds: IconType;
export declare const GiRelicBlade: IconType;
export declare const GiReloadGunBarrel: IconType;
export declare const GiRemedy: IconType;
export declare const GiRempart: IconType;
export declare const GiReptileTail: IconType;
export declare const GiResize: IconType;
export declare const GiResonance: IconType;
export declare const GiRestingVampire: IconType;
export declare const GiReticule: IconType;
export declare const GiRetroController: IconType;
export declare const GiReturnArrow: IconType;
export declare const GiRevolt: IconType;
export declare const GiRevolver: IconType;
export declare const GiRhinocerosHorn: IconType;
export declare const GiRialtoBridge: IconType;
export declare const GiRibbonMedal: IconType;
export declare const GiRibbonShield: IconType;
export declare const GiRibbon: IconType;
export declare const GiRibcage: IconType;
export declare const GiRiceCooker: IconType;
export declare const GiRifle: IconType;
export declare const GiRingBox: IconType;
export declare const GiRingMould: IconType;
export declare const GiRing: IconType;
export declare const GiRingedBeam: IconType;
export declare const GiRingedPlanet: IconType;
export declare const GiRingingAlarm: IconType;
export declare const GiRingingBell: IconType;
export declare const GiRingmaster: IconType;
export declare const GiRiotShield: IconType;
export declare const GiRiver: IconType;
export declare const GiRoad: IconType;
export declare const GiRoastChicken: IconType;
export declare const GiRobberHand: IconType;
export declare const GiRobberMask: IconType;
export declare const GiRobber: IconType;
export declare const GiRobe: IconType;
export declare const GiRobinHoodHat: IconType;
export declare const GiRobotAntennas: IconType;
export declare const GiRobotGolem: IconType;
export declare const GiRobotGrab: IconType;
export declare const GiRobotHelmet: IconType;
export declare const GiRobotLeg: IconType;
export declare const GiRockGolem: IconType;
export declare const GiRock: IconType;
export declare const GiRocketFlight: IconType;
export declare const GiRocketThruster: IconType;
export declare const GiRocket: IconType;
export declare const GiRockingChair: IconType;
export declare const GiRodOfAsclepius: IconType;
export declare const GiRogue: IconType;
export declare const GiRolledCloth: IconType;
export declare const GiRollerSkate: IconType;
export declare const GiRollingBomb: IconType;
export declare const GiRollingDiceCup: IconType;
export declare const GiRollingDices: IconType;
export declare const GiRollingEnergy: IconType;
export declare const GiRollingSuitcase: IconType;
export declare const GiRomanShield: IconType;
export declare const GiRomanToga: IconType;
export declare const GiRooster: IconType;
export declare const GiRootTip: IconType;
export declare const GiRopeBridge: IconType;
export declare const GiRopeCoil: IconType;
export declare const GiRopeDart: IconType;
export declare const GiRopeway: IconType;
export declare const GiRosaShield: IconType;
export declare const GiRose: IconType;
export declare const GiRotaryPhone: IconType;
export declare const GiRoughWound: IconType;
export declare const GiRoundBottomFlask: IconType;
export declare const GiRoundKnob: IconType;
export declare const GiRoundShield: IconType;
export declare const GiRoundSilo: IconType;
export declare const GiRoundStar: IconType;
export declare const GiRoundStrawBale: IconType;
export declare const GiRoundStruck: IconType;
export declare const GiRoundTable: IconType;
export declare const GiRoyalLove: IconType;
export declare const GiRss: IconType;
export declare const GiRubElHizb: IconType;
export declare const GiRubberBoot: IconType;
export declare const GiRugbyConversion: IconType;
export declare const GiRuleBook: IconType;
export declare const GiRun: IconType;
export declare const GiRuneStone: IconType;
export declare const GiRuneSword: IconType;
export declare const GiRunningNinja: IconType;
export declare const GiRunningShoe: IconType;
export declare const GiRupee: IconType;
export declare const GiRustySword: IconType;
export declare const GiSBrick: IconType;
export declare const GiSaberAndPistol: IconType;
export declare const GiSaberSlash: IconType;
export declare const GiSaberTooth: IconType;
export declare const GiSaberToothedCatHead: IconType;
export declare const GiSabersChoc: IconType;
export declare const GiSacrificialDagger: IconType;
export declare const GiSadCrab: IconType;
export declare const GiSaddle: IconType;
export declare const GiSafetyPin: IconType;
export declare const GiSagittarius: IconType;
export declare const GiSai: IconType;
export declare const GiSail: IconType;
export declare const GiSailboat: IconType;
export declare const GiSaintBasilCathedral: IconType;
export declare const GiSaiyanSuit: IconType;
export declare const GiSalamander: IconType;
export declare const GiSalmon: IconType;
export declare const GiSaloonDoors: IconType;
export declare const GiSaloon: IconType;
export declare const GiSaltShaker: IconType;
export declare const GiSamaraMosque: IconType;
export declare const GiSamuraiHelmet: IconType;
export declare const GiSamusHelmet: IconType;
export declare const GiSandCastle: IconType;
export declare const GiSandSnake: IconType;
export declare const GiSandal: IconType;
export declare const GiSandsOfTime: IconType;
export declare const GiSandstorm: IconType;
export declare const GiSandwich: IconType;
export declare const GiSaphir: IconType;
export declare const GiSarcophagus: IconType;
export declare const GiSasquatch: IconType;
export declare const GiSatelliteCommunication: IconType;
export declare const GiSattelite: IconType;
export declare const GiSaucepan: IconType;
export declare const GiSauropodHead: IconType;
export declare const GiSauropodSkeleton: IconType;
export declare const GiSausage: IconType;
export declare const GiSausagesRibbon: IconType;
export declare const GiSaveArrow: IconType;
export declare const GiSave: IconType;
export declare const GiSawClaw: IconType;
export declare const GiSawedOffShotgun: IconType;
export declare const GiSaxophone: IconType;
export declare const GiScabbard: IconType;
export declare const GiScaleMail: IconType;
export declare const GiScales: IconType;
export declare const GiScallop: IconType;
export declare const GiScalpelStrike: IconType;
export declare const GiScalpel: IconType;
export declare const GiScarWound: IconType;
export declare const GiScarabBeetle: IconType;
export declare const GiScarecrow: IconType;
export declare const GiSchoolBag: IconType;
export declare const GiSchoolOfFish: IconType;
export declare const GiScissors: IconType;
export declare const GiScooter: IconType;
export declare const GiScorpio: IconType;
export declare const GiScorpionTail: IconType;
export declare const GiScorpion: IconType;
export declare const GiScoutShip: IconType;
export declare const GiScreaming: IconType;
export declare const GiScreenImpact: IconType;
export declare const GiScrew: IconType;
export declare const GiScrewdriver: IconType;
export declare const GiScrollQuill: IconType;
export declare const GiScrollUnfurled: IconType;
export declare const GiScubaMask: IconType;
export declare const GiScubaTanks: IconType;
export declare const GiScythe: IconType;
export declare const GiSeaCliff: IconType;
export declare const GiSeaCreature: IconType;
export declare const GiSeaDragon: IconType;
export declare const GiSeaSerpent: IconType;
export declare const GiSeaStar: IconType;
export declare const GiSeaTurtle: IconType;
export declare const GiSeagull: IconType;
export declare const GiSeahorse: IconType;
export declare const GiSeatedMouse: IconType;
export declare const GiSecretBook: IconType;
export declare const GiSecretDoor: IconType;
export declare const GiSecurityGate: IconType;
export declare const GiSeedling: IconType;
export declare const GiSelect: IconType;
export declare const GiSelfLove: IconType;
export declare const GiSellCard: IconType;
export declare const GiSemiClosedEye: IconType;
export declare const GiSensuousness: IconType;
export declare const GiSentryGun: IconType;
export declare const GiSergeant: IconType;
export declare const GiSerratedSlash: IconType;
export declare const GiServerRack: IconType;
export declare const GiSesame: IconType;
export declare const GiSettingsKnobs: IconType;
export declare const GiSevenPointedStar: IconType;
export declare const GiSeveredHand: IconType;
export declare const GiSewedShell: IconType;
export declare const GiSewingMachine: IconType;
export declare const GiSewingNeedle: IconType;
export declare const GiSewingString: IconType;
export declare const GiSextant: IconType;
export declare const GiShadowFollower: IconType;
export declare const GiShadowGrasp: IconType;
export declare const GiShakingHands: IconType;
export declare const GiShamblingMound: IconType;
export declare const GiShamblingZombie: IconType;
export declare const GiShamrock: IconType;
export declare const GiShardSword: IconType;
export declare const GiShare: IconType;
export declare const GiSharkBite: IconType;
export declare const GiSharkFin: IconType;
export declare const GiSharkJaws: IconType;
export declare const GiSharpAxe: IconType;
export declare const GiSharpCrown: IconType;
export declare const GiSharpHalberd: IconType;
export declare const GiSharpLips: IconType;
export declare const GiSharpShuriken: IconType;
export declare const GiSharpSmile: IconType;
export declare const GiSharpedTeethSkull: IconType;
export declare const GiShatter: IconType;
export declare const GiShatteredGlass: IconType;
export declare const GiShatteredHeart: IconType;
export declare const GiShatteredSword: IconType;
export declare const GiShears: IconType;
export declare const GiSheep: IconType;
export declare const GiSheikahEye: IconType;
export declare const GiShepherdsCrook: IconType;
export declare const GiSherlockHolmes: IconType;
export declare const GiShieldBash: IconType;
export declare const GiShieldBounces: IconType;
export declare const GiShieldDisabled: IconType;
export declare const GiShieldEchoes: IconType;
export declare const GiShieldImpact: IconType;
export declare const GiShieldOpposition: IconType;
export declare const GiShieldReflect: IconType;
export declare const GiShield: IconType;
export declare const GiShieldcomb: IconType;
export declare const GiShiningClaw: IconType;
export declare const GiShiningHeart: IconType;
export declare const GiShiningSword: IconType;
export declare const GiShintoShrineMirror: IconType;
export declare const GiShintoShrine: IconType;
export declare const GiShinyApple: IconType;
export declare const GiShinyEntrance: IconType;
export declare const GiShinyIris: IconType;
export declare const GiShinyOmega: IconType;
export declare const GiShinyPurse: IconType;
export declare const GiShipBow: IconType;
export declare const GiShipWheel: IconType;
export declare const GiShipWreck: IconType;
export declare const GiShirtButton: IconType;
export declare const GiShirt: IconType;
export declare const GiShoebillStork: IconType;
export declare const GiShoonerSailboat: IconType;
export declare const GiShop: IconType;
export declare const GiShoppingBag: IconType;
export declare const GiShoppingCart: IconType;
export declare const GiShorts: IconType;
export declare const GiShotgunRounds: IconType;
export declare const GiShotgun: IconType;
export declare const GiShoulderArmor: IconType;
export declare const GiShoulderBag: IconType;
export declare const GiShoulderScales: IconType;
export declare const GiShouting: IconType;
export declare const GiShower: IconType;
export declare const GiShrimp: IconType;
export declare const GiShrug: IconType;
export declare const GiShurikenAperture: IconType;
export declare const GiShuriken: IconType;
export declare const GiShutRose: IconType;
export declare const GiShuttlecock: IconType;
export declare const GiSickle: IconType;
export declare const GiSideswipe: IconType;
export declare const GiSiegeRam: IconType;
export declare const GiSiegeTower: IconType;
export declare const GiSightDisabled: IconType;
export declare const GiSilence: IconType;
export declare const GiSilenced: IconType;
export declare const GiSilex: IconType;
export declare const GiSilverBullet: IconType;
export declare const GiSinagot: IconType;
export declare const GiSing: IconType;
export declare const GiSinkingShip: IconType;
export declare const GiSinkingTrap: IconType;
export declare const GiSinusoidalBeam: IconType;
export declare const GiSiren: IconType;
export declare const GiSittingDog: IconType;
export declare const GiSixEyes: IconType;
export declare const GiSkateboard: IconType;
export declare const GiSkeletalHand: IconType;
export declare const GiSkeletonInside: IconType;
export declare const GiSkeletonKey: IconType;
export declare const GiSkeleton: IconType;
export declare const GiSkiBoot: IconType;
export declare const GiSkidMark: IconType;
export declare const GiSkier: IconType;
export declare const GiSkills: IconType;
export declare const GiSkippingRope: IconType;
export declare const GiSkirt: IconType;
export declare const GiSkis: IconType;
export declare const GiSkullBolt: IconType;
export declare const GiSkullCrack: IconType;
export declare const GiSkullCrossedBones: IconType;
export declare const GiSkullInJar: IconType;
export declare const GiSkullMask: IconType;
export declare const GiSkullRing: IconType;
export declare const GiSkullSabertooth: IconType;
export declare const GiSkullShield: IconType;
export declare const GiSkullSignet: IconType;
export declare const GiSkullSlices: IconType;
export declare const GiSkullStaff: IconType;
export declare const GiSkullWithSyringe: IconType;
export declare const GiSlalom: IconType;
export declare const GiSlap: IconType;
export declare const GiSlashedShield: IconType;
export declare const GiSlaveryWhip: IconType;
export declare const GiSleepingBag: IconType;
export declare const GiSleepy: IconType;
export declare const GiSleevelessJacket: IconType;
export declare const GiSleevelessTop: IconType;
export declare const GiSlicedBread: IconType;
export declare const GiSlicedMushroom: IconType;
export declare const GiSlicedSausage: IconType;
export declare const GiSlicingArrow: IconType;
export declare const GiSlime: IconType;
export declare const GiSling: IconType;
export declare const GiSlingshot: IconType;
export declare const GiSlipknot: IconType;
export declare const GiSlippers: IconType;
export declare const GiSloth: IconType;
export declare const GiSlowBlob: IconType;
export declare const GiSlumberingSanctuary: IconType;
export declare const GiSly: IconType;
export declare const GiSmallFire: IconType;
export declare const GiSmallFishingSailboat: IconType;
export declare const GiSmart: IconType;
export declare const GiSmartphone: IconType;
export declare const GiSmashArrows: IconType;
export declare const GiSmitten: IconType;
export declare const GiSmokeBomb: IconType;
export declare const GiSmokingFinger: IconType;
export declare const GiSmokingOrb: IconType;
export declare const GiSmokingPipe: IconType;
export declare const GiSmokingVolcano: IconType;
export declare const GiSnailEyes: IconType;
export declare const GiSnail: IconType;
export declare const GiSnakeBite: IconType;
export declare const GiSnakeEgg: IconType;
export declare const GiSnakeJar: IconType;
export declare const GiSnakeSpiral: IconType;
export declare const GiSnakeTongue: IconType;
export declare const GiSnakeTotem: IconType;
export declare const GiSnake: IconType;
export declare const GiSnatch: IconType;
export declare const GiSniffingDog: IconType;
export declare const GiSnitchQuidditchBall: IconType;
export declare const GiSnorkel: IconType;
export declare const GiSnout: IconType;
export declare const GiSnowBottle: IconType;
export declare const GiSnowboard: IconType;
export declare const GiSnowflake1: IconType;
export declare const GiSnowflake2: IconType;
export declare const GiSnowing: IconType;
export declare const GiSnowman: IconType;
export declare const GiSoapExperiment: IconType;
export declare const GiSoap: IconType;
export declare const GiSoccerBall: IconType;
export declare const GiSoccerField: IconType;
export declare const GiSoccerKick: IconType;
export declare const GiSocks: IconType;
export declare const GiSodaCan: IconType;
export declare const GiSofa: IconType;
export declare const GiSolarPower: IconType;
export declare const GiSolarSystem: IconType;
export declare const GiSolarTime: IconType;
export declare const GiSolderingIron: IconType;
export declare const GiSolidLeaf: IconType;
export declare const GiSombrero: IconType;
export declare const GiSonicBoom: IconType;
export declare const GiSonicLightning: IconType;
export declare const GiSonicScreech: IconType;
export declare const GiSonicShoes: IconType;
export declare const GiSonicShout: IconType;
export declare const GiSoulVessel: IconType;
export declare const GiSoundOff: IconType;
export declare const GiSoundOn: IconType;
export declare const GiSoundWaves: IconType;
export declare const GiSouthAfricaFlag: IconType;
export declare const GiSouthAfrica: IconType;
export declare const GiSouthAmerica: IconType;
export declare const GiSouthKorea: IconType;
export declare const GiSpaceNeedle: IconType;
export declare const GiSpaceShuttle: IconType;
export declare const GiSpaceSuit: IconType;
export declare const GiSpaceship: IconType;
export declare const GiSpadeSkull: IconType;
export declare const GiSpade: IconType;
export declare const GiSpades: IconType;
export declare const GiSpain: IconType;
export declare const GiSpanner: IconType;
export declare const GiSparkPlug: IconType;
export declare const GiSparkSpirit: IconType;
export declare const GiSparkles: IconType;
export declare const GiSparklingSabre: IconType;
export declare const GiSparkyBomb: IconType;
export declare const GiSparrow: IconType;
export declare const GiSpartanHelmet: IconType;
export declare const GiSpartan: IconType;
export declare const GiSpatter: IconType;
export declare const GiSpawnNode: IconType;
export declare const GiSpeakerOff: IconType;
export declare const GiSpeaker: IconType;
export declare const GiSpearFeather: IconType;
export declare const GiSpearHook: IconType;
export declare const GiSpearfishing: IconType;
export declare const GiSpears: IconType;
export declare const GiSpectacleLenses: IconType;
export declare const GiSpectacles: IconType;
export declare const GiSpectreM4: IconType;
export declare const GiSpectre: IconType;
export declare const GiSpeedBoat: IconType;
export declare const GiSpeedometer: IconType;
export declare const GiSpellBook: IconType;
export declare const GiSpermWhale: IconType;
export declare const GiSpiderAlt: IconType;
export declare const GiSpiderBot: IconType;
export declare const GiSpiderEye: IconType;
export declare const GiSpiderFace: IconType;
export declare const GiSpiderMask: IconType;
export declare const GiSpiderWeb: IconType;
export declare const GiSpikeball: IconType;
export declare const GiSpikedArmor: IconType;
export declare const GiSpikedBall: IconType;
export declare const GiSpikedBat: IconType;
export declare const GiSpikedCollar: IconType;
export declare const GiSpikedDragonHead: IconType;
export declare const GiSpikedFence: IconType;
export declare const GiSpikedHalo: IconType;
export declare const GiSpikedMace: IconType;
export declare const GiSpikedShell: IconType;
export declare const GiSpikedShield: IconType;
export declare const GiSpikedShoulderArmor: IconType;
export declare const GiSpikedSnail: IconType;
export declare const GiSpikedTail: IconType;
export declare const GiSpikedTentacle: IconType;
export declare const GiSpikedTrunk: IconType;
export declare const GiSpikedWall: IconType;
export declare const GiSpikesFull: IconType;
export declare const GiSpikesHalf: IconType;
export declare const GiSpikesInit: IconType;
export declare const GiSpikes: IconType;
export declare const GiSpikyEclipse: IconType;
export declare const GiSpikyExplosion: IconType;
export declare const GiSpikyField: IconType;
export declare const GiSpikyPit: IconType;
export declare const GiSpikyWing: IconType;
export declare const GiSpill: IconType;
export declare const GiSpinalCoil: IconType;
export declare const GiSpineArrow: IconType;
export declare const GiSpinningBlades: IconType;
export declare const GiSpinningRibbons: IconType;
export declare const GiSpinningSword: IconType;
export declare const GiSpinningTop: IconType;
export declare const GiSpinningWheel: IconType;
export declare const GiSpiralArrow: IconType;
export declare const GiSpiralBloom: IconType;
export declare const GiSpiralBottle: IconType;
export declare const GiSpiralHilt: IconType;
export declare const GiSpiralLollipop: IconType;
export declare const GiSpiralShell: IconType;
export declare const GiSpiralTentacle: IconType;
export declare const GiSpiralThrust: IconType;
export declare const GiSplash: IconType;
export declare const GiSplashyStream: IconType;
export declare const GiSplitArrows: IconType;
export declare const GiSplitBody: IconType;
export declare const GiSplitCross: IconType;
export declare const GiSplurt: IconType;
export declare const GiSpockHand: IconType;
export declare const GiSpookyHouse: IconType;
export declare const GiSpoon: IconType;
export declare const GiSportMedal: IconType;
export declare const GiSpotedFlower: IconType;
export declare const GiSpottedArrowhead: IconType;
export declare const GiSpottedBug: IconType;
export declare const GiSpottedMushroom: IconType;
export declare const GiSpottedWound: IconType;
export declare const GiSpoutnik: IconType;
export declare const GiSpray: IconType;
export declare const GiSpring: IconType;
export declare const GiSprint: IconType;
export declare const GiSproutDisc: IconType;
export declare const GiSprout: IconType;
export declare const GiSpy: IconType;
export declare const GiSpyglass: IconType;
export declare const GiSquareBottle: IconType;
export declare const GiSquare: IconType;
export declare const GiSquib: IconType;
export declare const GiSquidHead: IconType;
export declare const GiSquid: IconType;
export declare const GiSquirrel: IconType;
export declare const GiSriLanka: IconType;
export declare const GiStabbedNote: IconType;
export declare const GiStable: IconType;
export declare const GiStack: IconType;
export declare const GiStagHead: IconType;
export declare const GiStahlhelm: IconType;
export declare const GiStairsCake: IconType;
export declare const GiStairsGoal: IconType;
export declare const GiStairs: IconType;
export declare const GiStakeHammer: IconType;
export declare const GiStakesFence: IconType;
export declare const GiStalactites: IconType;
export declare const GiStalagtite: IconType;
export declare const GiStamper: IconType;
export declare const GiStandingPotion: IconType;
export declare const GiStaplerHeavyDuty: IconType;
export declare const GiStaplerPneumatic: IconType;
export declare const GiStapler: IconType;
export declare const GiStarAltar: IconType;
export declare const GiStarCycle: IconType;
export declare const GiStarFlag: IconType;
export declare const GiStarFormation: IconType;
export declare const GiStarGate: IconType;
export declare const GiStarKey: IconType;
export declare const GiStarMedal: IconType;
export declare const GiStarProminences: IconType;
export declare const GiStarPupil: IconType;
export declare const GiStarSattelites: IconType;
export declare const GiStarShuriken: IconType;
export declare const GiStarSkull: IconType;
export declare const GiStarStruck: IconType;
export declare const GiStarSwirl: IconType;
export declare const GiStarfighter: IconType;
export declare const GiStarsStack: IconType;
export declare const GiStaryu: IconType;
export declare const GiStaticGuard: IconType;
export declare const GiStaticWaves: IconType;
export declare const GiStatic: IconType;
export declare const GiSteak: IconType;
export declare const GiStealthBomber: IconType;
export declare const GiSteamBlast: IconType;
export declare const GiSteamLocomotive: IconType;
export declare const GiSteam: IconType;
export declare const GiSteampunkGoggles: IconType;
export declare const GiSteelClaws: IconType;
export declare const GiSteelDoor: IconType;
export declare const GiSteeltoeBoots: IconType;
export declare const GiSteelwingEmblem: IconType;
export declare const GiSteeringWheel: IconType;
export declare const GiStegosaurusScales: IconType;
export declare const GiStethoscope: IconType;
export declare const GiSteyrAug: IconType;
export declare const GiStickFrame: IconType;
export declare const GiStickGrenade: IconType;
export declare const GiStickSplitting: IconType;
export declare const GiStickingPlaster: IconType;
export declare const GiStickyBoot: IconType;
export declare const GiStigmata: IconType;
export declare const GiStiletto: IconType;
export declare const GiStitchedWound: IconType;
export declare const GiStockpiles: IconType;
export declare const GiStomach: IconType;
export declare const GiStompTornado: IconType;
export declare const GiStomp: IconType;
export declare const GiStoneAxe: IconType;
export declare const GiStoneBlock: IconType;
export declare const GiStoneBridge: IconType;
export declare const GiStoneBust: IconType;
export declare const GiStoneCrafting: IconType;
export declare const GiStonePath: IconType;
export declare const GiStonePile: IconType;
export declare const GiStoneSpear: IconType;
export declare const GiStoneSphere: IconType;
export declare const GiStoneStack: IconType;
export declare const GiStoneTablet: IconType;
export declare const GiStoneThrone: IconType;
export declare const GiStoneTower: IconType;
export declare const GiStoneWall: IconType;
export declare const GiStoneWheel: IconType;
export declare const GiStonedSkull: IconType;
export declare const GiStopSign: IconType;
export declare const GiStopwatch: IconType;
export declare const GiStorkDelivery: IconType;
export declare const GiStrafe: IconType;
export declare const GiStraightPipe: IconType;
export declare const GiStrawberry: IconType;
export declare const GiStreetLight: IconType;
export declare const GiStrikingArrows: IconType;
export declare const GiStrikingBalls: IconType;
export declare const GiStrikingClamps: IconType;
export declare const GiStrikingDiamonds: IconType;
export declare const GiStrikingSplinter: IconType;
export declare const GiStripedSun: IconType;
export declare const GiStripedSword: IconType;
export declare const GiStrongMan: IconType;
export declare const GiStrong: IconType;
export declare const GiStrongbox: IconType;
export declare const GiStumpRegrowth: IconType;
export declare const GiStunGrenade: IconType;
export declare const GiSubmarineMissile: IconType;
export declare const GiSubmarine: IconType;
export declare const GiSubway: IconType;
export declare const GiSuckeredTentacle: IconType;
export declare const GiSugarCane: IconType;
export declare const GiSuicide: IconType;
export declare const GiSuitcase: IconType;
export declare const GiSuits: IconType;
export declare const GiSummits: IconType;
export declare const GiSunCloud: IconType;
export declare const GiSunPriest: IconType;
export declare const GiSunRadiations: IconType;
export declare const GiSunSpear: IconType;
export declare const GiSun: IconType;
export declare const GiSunbeams: IconType;
export declare const GiSundial: IconType;
export declare const GiSunflower: IconType;
export declare const GiSunglasses: IconType;
export declare const GiSunkenEye: IconType;
export declare const GiSunrise: IconType;
export declare const GiSunset: IconType;
export declare const GiSuperMushroom: IconType;
export declare const GiSupersonicArrow: IconType;
export declare const GiSupersonicBullet: IconType;
export declare const GiSurfBoard: IconType;
export declare const GiSurferVan: IconType;
export declare const GiSurprisedSkull: IconType;
export declare const GiSurprised: IconType;
export declare const GiSurroundedEye: IconType;
export declare const GiSurroundedShield: IconType;
export declare const GiSushis: IconType;
export declare const GiSuspensionBridge: IconType;
export declare const GiSuspicious: IconType;
export declare const GiSverdIFjell: IconType;
export declare const GiSwallow: IconType;
export declare const GiSwallower: IconType;
export declare const GiSwampBat: IconType;
export declare const GiSwamp: IconType;
export declare const GiSwanBreeze: IconType;
export declare const GiSwan: IconType;
export declare const GiSwapBag: IconType;
export declare const GiSwimfins: IconType;
export declare const GiSwipeCard: IconType;
export declare const GiSwirlRing: IconType;
export declare const GiSwirlString: IconType;
export declare const GiSwirledShell: IconType;
export declare const GiSwissArmyKnife: IconType;
export declare const GiSwitchWeapon: IconType;
export declare const GiSwitchblade: IconType;
export declare const GiSwitzerland: IconType;
export declare const GiSwordAltar: IconType;
export declare const GiSwordArray: IconType;
export declare const GiSwordBrandish: IconType;
export declare const GiSwordBreak: IconType;
export declare const GiSwordClash: IconType;
export declare const GiSwordHilt: IconType;
export declare const GiSwordInStone: IconType;
export declare const GiSwordMold: IconType;
export declare const GiSwordSlice: IconType;
export declare const GiSwordSmithing: IconType;
export declare const GiSwordSpade: IconType;
export declare const GiSwordSpin: IconType;
export declare const GiSwordTie: IconType;
export declare const GiSwordWound: IconType;
export declare const GiSwordman: IconType;
export declare const GiSwordsEmblem: IconType;
export declare const GiSwordsPower: IconType;
export declare const GiSwordwoman: IconType;
export declare const GiSydneyOperaHouse: IconType;
export declare const GiSyringe: IconType;
export declare const GiTBrick: IconType;
export declare const GiTRexSkull: IconType;
export declare const GiTShirt: IconType;
export declare const GiTabiBoot: IconType;
export declare const GiTable: IconType;
export declare const GiTablet: IconType;
export declare const GiTabletopPlayers: IconType;
export declare const GiTacos: IconType;
export declare const GiTadpole: IconType;
export declare const GiTakeMyMoney: IconType;
export declare const GiTalk: IconType;
export declare const GiTallBridge: IconType;
export declare const GiTambourine: IconType;
export declare const GiTangerine: IconType;
export declare const GiTankTop: IconType;
export declare const GiTankTread: IconType;
export declare const GiTank: IconType;
export declare const GiTanzania: IconType;
export declare const GiTap: IconType;
export declare const GiTapir: IconType;
export declare const GiTargetArrows: IconType;
export declare const GiTargetDummy: IconType;
export declare const GiTargetLaser: IconType;
export declare const GiTargetPoster: IconType;
export declare const GiTargetPrize: IconType;
export declare const GiTargetShot: IconType;
export declare const GiTargeted: IconType;
export declare const GiTargeting: IconType;
export declare const GiTatteredBanner: IconType;
export declare const GiTaurus: IconType;
export declare const GiTavernSign: IconType;
export declare const GiTeacher: IconType;
export declare const GiTeamDowngrade: IconType;
export declare const GiTeamIdea: IconType;
export declare const GiTeamUpgrade: IconType;
export declare const GiTeapotLeaves: IconType;
export declare const GiTeapot: IconType;
export declare const GiTearTracks: IconType;
export declare const GiTearing: IconType;
export declare const GiTec9: IconType;
export declare const GiTechnoHeart: IconType;
export declare const GiTeePipe: IconType;
export declare const GiTelefrag: IconType;
export declare const GiTelepathy: IconType;
export declare const GiTeleport: IconType;
export declare const GiTelescopicBaton: IconType;
export declare const GiTellerMine: IconType;
export declare const GiTemplarEye: IconType;
export declare const GiTemplarHeart: IconType;
export declare const GiTemplarShield: IconType;
export declare const GiTempleDoor: IconType;
export declare const GiTempleGate: IconType;
export declare const GiTemporaryShield: IconType;
export declare const GiTemptation: IconType;
export declare const GiTennisBall: IconType;
export declare const GiTennisCourt: IconType;
export declare const GiTennisRacket: IconType;
export declare const GiTensionSnowflake: IconType;
export declare const GiTentacleHeart: IconType;
export declare const GiTentacleStrike: IconType;
export declare const GiTentaclesBarrier: IconType;
export declare const GiTentaclesSkull: IconType;
export declare const GiTentacurl: IconType;
export declare const GiTerror: IconType;
export declare const GiTeslaCoil: IconType;
export declare const GiTeslaTurret: IconType;
export declare const GiTesla: IconType;
export declare const GiTestTubes: IconType;
export declare const GiTexas: IconType;
export declare const GiTheaterCurtains: IconType;
export declare const GiTheater: IconType;
export declare const GiThermometerCold: IconType;
export declare const GiThermometerHot: IconType;
export declare const GiThermometerScale: IconType;
export declare const GiThink: IconType;
export declare const GiThirdEye: IconType;
export declare const GiThompsonM1: IconType;
export declare const GiThompsonM1928: IconType;
export declare const GiThorFist: IconType;
export declare const GiThorHammer: IconType;
export declare const GiThornHelix: IconType;
export declare const GiThornedArrow: IconType;
export declare const GiThornyTentacle: IconType;
export declare const GiThornyVine: IconType;
export declare const GiThreeBurningBalls: IconType;
export declare const GiThreeFriends: IconType;
export declare const GiThreeKeys: IconType;
export declare const GiThreeLeaves: IconType;
export declare const GiThreePointedShuriken: IconType;
export declare const GiThroneKing: IconType;
export declare const GiThrowingBall: IconType;
export declare const GiThrownCharcoal: IconType;
export declare const GiThrownDaggers: IconType;
export declare const GiThrownKnife: IconType;
export declare const GiThrownSpear: IconType;
export declare const GiThrustBend: IconType;
export declare const GiThrust: IconType;
export declare const GiThumbDown: IconType;
export declare const GiThumbUp: IconType;
export declare const GiThunderBlade: IconType;
export declare const GiThunderSkull: IconType;
export declare const GiThunderStruck: IconType;
export declare const GiThunderball: IconType;
export declare const GiThwomp: IconType;
export declare const GiTiara: IconType;
export declare const GiTicTacToe: IconType;
export declare const GiTick: IconType;
export declare const GiTicket: IconType;
export declare const GiTie: IconType;
export declare const GiTiedScroll: IconType;
export declare const GiTigerHead: IconType;
export declare const GiTiger: IconType;
export declare const GiTightrope: IconType;
export declare const GiTimeBomb: IconType;
export declare const GiTimeDynamite: IconType;
export declare const GiTimeSynchronization: IconType;
export declare const GiTimeTrap: IconType;
export declare const GiTinker: IconType;
export declare const GiTipi: IconType;
export declare const GiTireIronCross: IconType;
export declare const GiTireIron: IconType;
export declare const GiTireTracks: IconType;
export declare const GiTiredEye: IconType;
export declare const GiToadTeeth: IconType;
export declare const GiToaster: IconType;
export declare const GiToggles: IconType;
export declare const GiToken: IconType;
export declare const GiTomahawk: IconType;
export declare const GiTomato: IconType;
export declare const GiTombstone: IconType;
export declare const GiTongue: IconType;
export declare const GiToolbox: IconType;
export declare const GiTooth: IconType;
export declare const GiToothbrush: IconType;
export declare const GiTopHat: IconType;
export declare const GiTopPaw: IconType;
export declare const GiTopaz: IconType;
export declare const GiTorc: IconType;
export declare const GiTorch: IconType;
export declare const GiTornadoDiscs: IconType;
export declare const GiTornado: IconType;
export declare const GiTorpedo: IconType;
export declare const GiTortoise: IconType;
export declare const GiTotemHead: IconType;
export declare const GiTotemMask: IconType;
export declare const GiTotem: IconType;
export declare const GiToucan: IconType;
export declare const GiTowTruck: IconType;
export declare const GiTowel: IconType;
export declare const GiTowerBridge: IconType;
export declare const GiTowerFall: IconType;
export declare const GiTowerFlag: IconType;
export declare const GiToyMallet: IconType;
export declare const GiTrackedRobot: IconType;
export declare const GiTrade: IconType;
export declare const GiTrafficCone: IconType;
export declare const GiTrafficLightsGreen: IconType;
export declare const GiTrafficLightsOrange: IconType;
export declare const GiTrafficLightsReadyToGo: IconType;
export declare const GiTrafficLightsRed: IconType;
export declare const GiTrail: IconType;
export declare const GiTrample: IconType;
export declare const GiTransform: IconType;
export declare const GiTransfuse: IconType;
export declare const GiTransparentSlime: IconType;
export declare const GiTransparentTubes: IconType;
export declare const GiTransportationRings: IconType;
export declare const GiTrapMask: IconType;
export declare const GiTrashCan: IconType;
export declare const GiTravelDress: IconType;
export declare const GiTread: IconType;
export declare const GiTreasureMap: IconType;
export declare const GiTrebuchet: IconType;
export declare const GiTreeBeehive: IconType;
export declare const GiTreeBranch: IconType;
export declare const GiTreeDoor: IconType;
export declare const GiTreeFace: IconType;
export declare const GiTreeGrowth: IconType;
export declare const GiTreeRoots: IconType;
export declare const GiTreeSwing: IconType;
export declare const GiTreehouse: IconType;
export declare const GiTrefoilLily: IconType;
export declare const GiTrefoilShuriken: IconType;
export declare const GiTrenchAssault: IconType;
export declare const GiTrenchBodyArmor: IconType;
export declare const GiTrenchKnife: IconType;
export declare const GiTrenchSpade: IconType;
export declare const GiTriangleTarget: IconType;
export declare const GiTribalGear: IconType;
export declare const GiTribalMask: IconType;
export declare const GiTribalPendant: IconType;
export declare const GiTribalShield: IconType;
export declare const GiTribunalJury: IconType;
export declare const GiTriceratopsHead: IconType;
export declare const GiTridentShield: IconType;
export declare const GiTrident: IconType;
export declare const GiTriforce: IconType;
export declare const GiTriggerHurt: IconType;
export declare const GiTrilobite: IconType;
export declare const GiTrinacria: IconType;
export declare const GiTriorb: IconType;
export declare const GiTripleBeak: IconType;
export declare const GiTripleClaws: IconType;
export declare const GiTripleCorn: IconType;
export declare const GiTripleGate: IconType;
export declare const GiTripleLock: IconType;
export declare const GiTripleNeedle: IconType;
export declare const GiTriplePlier: IconType;
export declare const GiTripleScratches: IconType;
export declare const GiTripleShells: IconType;
export declare const GiTripleSkulls: IconType;
export declare const GiTripleYin: IconType;
export declare const GiTripwire: IconType;
export declare const GiTriquetra: IconType;
export declare const GiTrireme: IconType;
export declare const GiTritonHead: IconType;
export declare const GiTroglodyte: IconType;
export declare const GiTrojanHorse: IconType;
export declare const GiTroll: IconType;
export declare const GiTrombone: IconType;
export declare const GiTronArrow: IconType;
export declare const GiTrophiesShelf: IconType;
export declare const GiTrophyCup: IconType;
export declare const GiTrophy: IconType;
export declare const GiTropicalFish: IconType;
export declare const GiTrousers: IconType;
export declare const GiTrowel: IconType;
export declare const GiTruce: IconType;
export declare const GiTruck: IconType;
export declare const GiTrumpetFlag: IconType;
export declare const GiTrumpet: IconType;
export declare const GiTrunkMushroom: IconType;
export declare const GiTuba: IconType;
export declare const GiTumbleweed: IconType;
export declare const GiTumor: IconType;
export declare const GiTumulus: IconType;
export declare const GiTunePitch: IconType;
export declare const GiTunisia: IconType;
export declare const GiTurban: IconType;
export declare const GiTurbine: IconType;
export declare const GiTurd: IconType;
export declare const GiTurnstile: IconType;
export declare const GiTurret: IconType;
export declare const GiTurtleShell: IconType;
export declare const GiTurtle: IconType;
export declare const GiTusksFlag: IconType;
export declare const GiTvRemote: IconType;
export declare const GiTvTower: IconType;
export declare const GiTv: IconType;
export declare const GiTwinShell: IconType;
export declare const GiTwirlCenter: IconType;
export declare const GiTwirlyFlower: IconType;
export declare const GiTwister: IconType;
export declare const GiTwoCoins: IconType;
export declare const GiTwoFeathers: IconType;
export declare const GiTwoHandedSword: IconType;
export declare const GiTwoShadows: IconType;
export declare const GiTyre: IconType;
export declare const GiUbisoftSun: IconType;
export declare const GiUdder: IconType;
export declare const GiUfo: IconType;
export declare const GiUltrasound: IconType;
export declare const GiUluru: IconType;
export declare const GiUmbrellaBayonet: IconType;
export declare const GiUmbrella: IconType;
export declare const GiUnbalanced: IconType;
export declare const GiUncertainty: IconType;
export declare const GiUndergroundCave: IconType;
export declare const GiUnderhand: IconType;
export declare const GiUnderwearShorts: IconType;
export declare const GiUnderwear: IconType;
export declare const GiUnfriendlyFire: IconType;
export declare const GiUnicorn: IconType;
export declare const GiUnicycle: IconType;
export declare const GiUnionJack: IconType;
export declare const GiUnlitBomb: IconType;
export declare const GiUnlitCandelabra: IconType;
export declare const GiUnlocking: IconType;
export declare const GiUnplugged: IconType;
export declare const GiUnstableOrb: IconType;
export declare const GiUnstableProjectile: IconType;
export declare const GiUpCard: IconType;
export declare const GiUpgrade: IconType;
export declare const GiUprising: IconType;
export declare const GiUrsaMajor: IconType;
export declare const GiUruguay: IconType;
export declare const GiUsaFlag: IconType;
export declare const GiUsable: IconType;
export declare const GiUsbKey: IconType;
export declare const GiUshanka: IconType;
export declare const GiUzi: IconType;
export declare const GiVacuumCleaner: IconType;
export declare const GiValley: IconType;
export declare const GiValve: IconType;
export declare const GiVampireCape: IconType;
export declare const GiVampireDracula: IconType;
export declare const GiVanDammeSplit: IconType;
export declare const GiVanillaFlower: IconType;
export declare const GiVelocipede: IconType;
export declare const GiVelociraptorTracks: IconType;
export declare const GiVelociraptor: IconType;
export declare const GiVendingMachine: IconType;
export declare const GiVenezuela: IconType;
export declare const GiVenusFlytrap: IconType;
export declare const GiVenusOfWillendorf: IconType;
export declare const GiVerticalBanner: IconType;
export declare const GiVerticalFlip: IconType;
export declare const GiVhs: IconType;
export declare const GiVial: IconType;
export declare const GiVibratingBall: IconType;
export declare const GiVibratingShield: IconType;
export declare const GiVibratingSmartphone: IconType;
export declare const GiVideoCamera: IconType;
export declare const GiVideoConference: IconType;
export declare const GiVikingChurch: IconType;
export declare const GiVikingHead: IconType;
export declare const GiVikingHelmet: IconType;
export declare const GiVikingLonghouse: IconType;
export declare const GiVikingShield: IconType;
export declare const GiVileFluid: IconType;
export declare const GiVillage: IconType;
export declare const GiVineFlower: IconType;
export declare const GiVineLeaf: IconType;
export declare const GiVineWhip: IconType;
export declare const GiVines: IconType;
export declare const GiVintageRobot: IconType;
export declare const GiViola: IconType;
export declare const GiViolin: IconType;
export declare const GiVirgo: IconType;
export declare const GiVirtualMarker: IconType;
export declare const GiVirus: IconType;
export declare const GiVisoredHelm: IconType;
export declare const GiVitruvianMan: IconType;
export declare const GiVolcano: IconType;
export declare const GiVolleyballBall: IconType;
export declare const GiVomiting: IconType;
export declare const GiVoodooDoll: IconType;
export declare const GiVortex: IconType;
export declare const GiVote: IconType;
export declare const GiVrHeadset: IconType;
export declare const GiVulture: IconType;
export declare const GiVuvuzelas: IconType;
export declare const GiWalk: IconType;
export declare const GiWalkieTalkie: IconType;
export declare const GiWalkingBoot: IconType;
export declare const GiWalkingScout: IconType;
export declare const GiWalkingTurret: IconType;
export declare const GiWallLight: IconType;
export declare const GiWallet: IconType;
export declare const GiWalrusHead: IconType;
export declare const GiWaltherPpk: IconType;
export declare const GiWantedReward: IconType;
export declare const GiWarAxe: IconType;
export declare const GiWarBonnet: IconType;
export declare const GiWarPick: IconType;
export declare const GiWarhammer: IconType;
export declare const GiWarlockEye: IconType;
export declare const GiWarlockHood: IconType;
export declare const GiWarpPipe: IconType;
export declare const GiWashingMachine: IconType;
export declare const GiWaspSting: IconType;
export declare const GiWatch: IconType;
export declare const GiWatchtower: IconType;
export declare const GiWaterBolt: IconType;
export declare const GiWaterBottle: IconType;
export declare const GiWaterDivinerStick: IconType;
export declare const GiWaterDrop: IconType;
export declare const GiWaterFlask: IconType;
export declare const GiWaterFountain: IconType;
export declare const GiWaterGallon: IconType;
export declare const GiWaterGun: IconType;
export declare const GiWaterMill: IconType;
export declare const GiWaterPolo: IconType;
export declare const GiWaterRecycling: IconType;
export declare const GiWaterSplash: IconType;
export declare const GiWaterTank: IconType;
export declare const GiWaterTower: IconType;
export declare const GiWaterfall: IconType;
export declare const GiWateringCan: IconType;
export declare const GiWatermelon: IconType;
export declare const GiWaveCrest: IconType;
export declare const GiWaveStrike: IconType;
export declare const GiWaveSurfer: IconType;
export declare const GiWaves: IconType;
export declare const GiWavyChains: IconType;
export declare const GiWavyItinerary: IconType;
export declare const GiWaxSeal: IconType;
export declare const GiWaxTablet: IconType;
export declare const GiWebSpit: IconType;
export declare const GiWeightCrush: IconType;
export declare const GiWeightLiftingDown: IconType;
export declare const GiWeightLiftingUp: IconType;
export declare const GiWeightScale: IconType;
export declare const GiWeight: IconType;
export declare const GiWell: IconType;
export declare const GiWerewolf: IconType;
export declare const GiWesternHat: IconType;
export declare const GiWhaleTail: IconType;
export declare const GiWheat: IconType;
export declare const GiWheelbarrow: IconType;
export declare const GiWhip: IconType;
export declare const GiWhiplash: IconType;
export declare const GiWhirlpoolShuriken: IconType;
export declare const GiWhirlwind: IconType;
export declare const GiWhisk: IconType;
export declare const GiWhistle: IconType;
export declare const GiWhiteBook: IconType;
export declare const GiWhiteTower: IconType;
export declare const GiWideArrowDunk: IconType;
export declare const GiWifiRouter: IconType;
export declare const GiWildfires: IconType;
export declare const GiWilliamTellSkull: IconType;
export declare const GiWilliamTell: IconType;
export declare const GiWillowTree: IconType;
export declare const GiWinchesterRifle: IconType;
export declare const GiWindHole: IconType;
export declare const GiWindSlap: IconType;
export declare const GiWindTurbine: IconType;
export declare const GiWindchimes: IconType;
export declare const GiWindmill: IconType;
export declare const GiWindowBars: IconType;
export declare const GiWindow: IconType;
export declare const GiWindpump: IconType;
export declare const GiWindsock: IconType;
export declare const GiWindyStripes: IconType;
export declare const GiWineBottle: IconType;
export declare const GiWineGlass: IconType;
export declare const GiWingCloak: IconType;
export declare const GiWingedArrow: IconType;
export declare const GiWingedEmblem: IconType;
export declare const GiWingedLeg: IconType;
export declare const GiWingedScepter: IconType;
export declare const GiWingedShield: IconType;
export declare const GiWingedSword: IconType;
export declare const GiWingfoot: IconType;
export declare const GiWinterGloves: IconType;
export declare const GiWinterHat: IconType;
export declare const GiWireCoil: IconType;
export declare const GiWireframeGlobe: IconType;
export declare const GiWisdom: IconType;
export declare const GiWitchFace: IconType;
export declare const GiWitchFlight: IconType;
export declare const GiWizardFace: IconType;
export declare const GiWizardStaff: IconType;
export declare const GiWok: IconType;
export declare const GiWolfHead: IconType;
export declare const GiWolfHowl: IconType;
export declare const GiWolfTrap: IconType;
export declare const GiWolverineClaws: IconType;
export declare const GiWomanElfFace: IconType;
export declare const GiWoodAxe: IconType;
export declare const GiWoodBeam: IconType;
export declare const GiWoodCabin: IconType;
export declare const GiWoodCanoe: IconType;
export declare const GiWoodClub: IconType;
export declare const GiWoodFrame: IconType;
export declare const GiWoodPile: IconType;
export declare const GiWoodStick: IconType;
export declare const GiWoodenChair: IconType;
export declare const GiWoodenClogs: IconType;
export declare const GiWoodenCrate: IconType;
export declare const GiWoodenDoor: IconType;
export declare const GiWoodenFence: IconType;
export declare const GiWoodenHelmet: IconType;
export declare const GiWoodenPegleg: IconType;
export declare const GiWoodenPier: IconType;
export declare const GiWoodenSign: IconType;
export declare const GiWool: IconType;
export declare const GiWorld: IconType;
export declare const GiWormMouth: IconType;
export declare const GiWorms: IconType;
export declare const GiWorriedEyes: IconType;
export declare const GiWrappedHeart: IconType;
export declare const GiWrappedSweet: IconType;
export declare const GiWrappingStar: IconType;
export declare const GiWreckingBall: IconType;
export declare const GiWrench: IconType;
export declare const GiWyvern: IconType;
export declare const GiXylophone: IconType;
export declare const GiYarn: IconType;
export declare const GiYinYang: IconType;
export declare const GiYunluo: IconType;
export declare const GiZBrick: IconType;
export declare const GiZatGun: IconType;
export declare const GiZebraShield: IconType;
export declare const GiZeppelin: IconType;
export declare const GiZeusSword: IconType;
export declare const GiZigArrow: IconType;
export declare const GiZigzagCage: IconType;
export declare const GiZigzagHieroglyph: IconType;
export declare const GiZigzagLeaf: IconType;
export declare const GiZigzagTune: IconType;
export declare const GiZipper: IconType;
