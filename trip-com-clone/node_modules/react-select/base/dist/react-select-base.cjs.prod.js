'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var Select = require('../../dist/Select-36d15f93.cjs.prod.js');
require('@babel/runtime/helpers/extends');
require('@babel/runtime/helpers/objectSpread2');
require('@babel/runtime/helpers/classCallCheck');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/inherits');
require('@babel/runtime/helpers/createSuper');
require('@babel/runtime/helpers/toConsumableArray');
require('react');
require('../../dist/index-665c4ed8.cjs.prod.js');
require('@emotion/react');
require('@babel/runtime/helpers/slicedToArray');
require('@babel/runtime/helpers/objectWithoutProperties');
require('@babel/runtime/helpers/typeof');
require('@babel/runtime/helpers/taggedTemplateLiteral');
require('@babel/runtime/helpers/defineProperty');
require('react-dom');
require('@floating-ui/dom');
require('use-isomorphic-layout-effect');
require('memoize-one');



exports["default"] = Select.Select;
exports.defaultProps = Select.defaultProps;
